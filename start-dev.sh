#!/bin/bash

# Chatwoot 开发环境快速启动脚本
# 一键启动完整的开发环境

set -e

echo "🚀 启动 Chatwoot 开发环境..."

# 检查环境文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境配置文件..."
    cp .env.development .env
    echo "✅ 环境配置文件已创建"
fi

# 创建必要的目录
mkdir -p docker/postgres/init
mkdir -p docker/redis
mkdir -p scripts

# 启动服务
echo "🐳 启动 Docker 服务..."
docker-compose -f docker-compose.dev.yml up -d

echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f docker-compose.dev.yml ps

echo ""
echo "🎉 Chatwoot 开发环境启动完成！"
echo ""
echo "📱 访问地址："
echo "  🌐 主应用:           http://localhost:3000"
echo "  ⚡ Vite 开发服务器:   http://localhost:3036"
echo "  📧 MailHog 邮件测试:  http://localhost:8025"
echo "  🗄️  Adminer 数据库:   http://localhost:8080"
echo "  🔴 Redis Commander:  http://localhost:8081"
echo ""
echo "🛠️  常用命令："
echo "  查看日志:    docker-compose -f docker-compose.dev.yml logs -f"
echo "  停止服务:    docker-compose -f docker-compose.dev.yml down"
echo "  进入容器:    docker-compose -f docker-compose.dev.yml exec rails /bin/sh"
echo "  数据库迁移:  docker-compose -f docker-compose.dev.yml exec rails bundle exec rails db:migrate"
echo ""
echo "📚 更多命令请使用: ./scripts/dev.sh help"
