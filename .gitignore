# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore the default SQLite database.
/db/*.sqlite3
/db/*.sqlite3-journal

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep
*.mmdb

# Ignore Byebug command history file.
.byebug_history
.DS_Store
*.log
# Ignore application configuration
node_modules
master.key
*.rdb

# Ignore env files
.env

public/uploads
public/packs*
public/assets/administrate*
public/assets/action*.js
public/assets/activestorage*.js
public/assets/trix*
public/assets/belongs_to*.js
public/assets/manifest*.js
public/assets/manifest*.js
public/assets/*.js.gz
public/assets/secretField*
public/assets/.sprockets-manifest-*.json

# VIM files
*.swp
*.swo
*.un~
.jest-cache

# ignore jetbrains IDE files
.idea

# coverage report
buildreports
coverage

/storage

# ignore packages
node_modules
package-lock.json

*.dump


# cypress
test/cypress/videos/*

/config/master.key
/config/*.enc


# yalc for local testing
.yalc
yalc.lock

/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity

# Vite Ruby
/public/vite*
# Vite uses dotenv and suggests to ignore local-only env files. See
# https://vitejs.dev/guide/env-and-mode.html#env-files
*.local


# TextEditors & AI Agents config files
.vscode
.claude/settings.local.json
.cursor
CLAUDE.local.md
