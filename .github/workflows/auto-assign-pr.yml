name: Auto-assign PR to Author

on:
  pull_request:
    types: [opened]

jobs:
  auto-assign:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Auto-assign PR to author
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const pull_number = context.payload.pull_request.number;
            const author = context.payload.pull_request.user.login;
            
            await github.rest.issues.addAssignees({
              owner,
              repo,
              issue_number: pull_number,
              assignees: [author]
            });
            
            console.log(`Assigned PR #${pull_number} to ${author}`);