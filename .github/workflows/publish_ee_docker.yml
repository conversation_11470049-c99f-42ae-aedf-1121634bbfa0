# #
# # This action will publish Chatwoot EE docker image.
# # This is set to run against merges to develop, master
# # and when tags are created.
# #

name: Publish Chatwoot EE docker images

on:
  push:
    branches:
      - develop
      - master
    tags:
      - v*
  workflow_dispatch:

env:
  DOCKER_REPO: chatwoot/chatwoot

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: linux/amd64
            runner: ubuntu-latest
          - platform: linux/arm64
            runner: ubuntu-22.04-arm
    runs-on: ${{ matrix.runner }}
    env:
      GIT_REF: ${{ github.head_ref || github.ref_name }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Set Chatwoot edition
        run: |
          echo -en '\nENV CW_EDITION="ee"' >> docker/Dockerfile

      - name: Set Docker Tags
        run: |
          SANITIZED_REF=$(echo "$GIT_REF" | sed 's/\//-/g')
          if [ "${{ github.ref_name }}" = "master" ]; then
            echo "DOCKER_TAG=${DOCKER_REPO}:latest" >> $GITHUB_ENV
          else
            echo "DOCKER_TAG=${DOCKER_REPO}:${SANITIZED_REF}" >> $GITHUB_ENV
          fi

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: docker/Dockerfile
          platforms: ${{ matrix.platform }}
          push: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
          outputs: type=image,name=${{ env.DOCKER_REPO }},push-by-digest=true,name-canonical=true,push=true

      - name: Export digest
        run: |
          mkdir -p ${{ runner.temp }}/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "${{ runner.temp }}/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 1

  merge:
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/digests
          pattern: digests-*
          merge-multiple: true

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create manifest list and push
        working-directory: ${{ runner.temp }}/digests
        env:
          GIT_REF: ${{ github.head_ref || github.ref_name }}
        run: |
          SANITIZED_REF=$(echo "$GIT_REF" | sed 's/\//-/g')
          if [ "${{ github.ref_name }}" = "master" ]; then
            TAG="${DOCKER_REPO}:latest"
          else
            TAG="${DOCKER_REPO}:${SANITIZED_REF}"
          fi
          
          docker buildx imagetools create -t $TAG \
            $(printf '${{ env.DOCKER_REPO }}@sha256:%s ' *)

      - name: Inspect image
        env:
          GIT_REF: ${{ github.head_ref || github.ref_name }}
        run: |
          SANITIZED_REF=$(echo "$GIT_REF" | sed 's/\//-/g')
          if [ "${{ github.ref_name }}" = "master" ]; then
            TAG="${DOCKER_REPO}:latest"
          else
            TAG="${DOCKER_REPO}:${SANITIZED_REF}"
          fi
          
          docker buildx imagetools inspect $TAG
