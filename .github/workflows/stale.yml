# This workflow warns and then closes PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '28 3 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      pull-requests: write

    steps:
    - uses: actions/stale@v5
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        days-before-issue-close: -1,
        days-before-issue-stale: -1
        days-before-pr-close: -1,
        days-before-pr-stale: 30,
        stale-pr-message: '🐢 Turtley slow progress alert! This pull request has been idle for over 30 days. Can we please speed things up and either merge it or release it back into the wild?'
        stale-pr-label: 'stale'
