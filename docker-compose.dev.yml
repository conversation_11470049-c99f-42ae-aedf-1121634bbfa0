# Chatwoot 开发环境 Docker Compose 配置
# 使用国内镜像源优化，支持代码热重载和开发调试
version: '3.8'

services:
  # 基础配置模板
  base: &base
    build:
      context: .
      dockerfile: ./docker/Dockerfile
      args:
        BUNDLE_WITHOUT: ''
        EXECJS_RUNTIME: 'Node'
        RAILS_ENV: 'development'
        RAILS_SERVE_STATIC_FILES: 'false'
    tty: true
    stdin_open: true
    image: chatwoot:development
    env_file: .env.development
    networks:
      - chatwoot-network
    restart: unless-stopped

  # Rails 主应用服务
  rails:
    <<: *base
    build:
      context: .
      dockerfile: ./docker/dockerfiles/rails.Dockerfile
    image: chatwoot-rails:development
    container_name: chatwoot-rails-dev
    volumes:
      - ./:/app:delegated
      - node_modules:/app/node_modules
      - packs:/app/public/packs
      - cache:/app/tmp/cache
      - bundle:/usr/local/bundle
      - rails_logs:/app/log
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      vite:
        condition: service_started
      mailhog:
        condition: service_started
      sidekiq:
        condition: service_started
    ports:
      - "3000:3000"
    environment:
      - VITE_DEV_SERVER_HOST=vite
      - NODE_ENV=development
      - RAILS_ENV=development
      - RAILS_LOG_TO_STDOUT=true
      - BUNDLE_PATH=/usr/local/bundle
    entrypoint: docker/entrypoints/rails.sh
    command: ["bundle", "exec", "rails", "s", "-p", "3000", "-b", "0.0.0.0"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.chatwoot.rule=Host(`localhost`)"
      - "traefik.http.services.chatwoot.loadbalancer.server.port=3000"

  # Sidekiq 后台任务处理服务
  sidekiq:
    <<: *base
    image: chatwoot-rails:development
    container_name: chatwoot-sidekiq-dev
    volumes:
      - ./:/app:delegated
      - node_modules:/app/node_modules
      - packs:/app/public/packs
      - cache:/app/tmp/cache
      - bundle:/usr/local/bundle
      - sidekiq_logs:/app/log
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mailhog:
        condition: service_started
    environment:
      - NODE_ENV=development
      - RAILS_ENV=development
      - RAILS_LOG_TO_STDOUT=true
    command: ["bundle", "exec", "sidekiq", "-C", "config/sidekiq.yml"]
    healthcheck:
      test: ["CMD", "bundle", "exec", "sidekiqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Vite 前端开发服务器
  vite:
    <<: *base
    build:
      context: .
      dockerfile: ./docker/dockerfiles/vite.Dockerfile
    image: chatwoot-vite:development
    container_name: chatwoot-vite-dev
    volumes:
      - ./:/app:delegated
      - node_modules:/app/node_modules
      - packs:/app/public/packs
      - cache:/app/tmp/cache
      - bundle:/usr/local/bundle
    ports:
      - "3036:3036"  # Vite 开发服务器
    environment:
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - VITE_DEV_SERVER_PORT=3036
      - NODE_ENV=development
      - RAILS_ENV=development
      - HMR_HOST=localhost
      - HMR_PORT=3036
    entrypoint: docker/entrypoints/vite.sh
    command: bin/vite dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3036"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # PostgreSQL 数据库服务
  postgres:
    image: pgvector/pgvector:pg16
    container_name: chatwoot-postgres-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=chatwoot_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=chatwoot_dev_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - TZ=Asia/Shanghai
    networks:
      - chatwoot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d chatwoot_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=vector
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c max_connections=200

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: chatwoot-redis-dev
    restart: unless-stopped
    command: ["sh", "-c", "redis-server --requirepass \"$REDIS_PASSWORD\" --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru"]
    env_file: .env.development
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - chatwoot-network
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # MailHog 邮件测试服务
  mailhog:
    image: mailhog/mailhog
    container_name: chatwoot-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP 端口
      - "8025:8025"  # Web UI 端口
    networks:
      - chatwoot-network
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Adminer 数据库管理工具 (可选)
  adminer:
    image: adminer:latest
    container_name: chatwoot-adminer-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - chatwoot-network
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
      - TZ=Asia/Shanghai
    depends_on:
      postgres:
        condition: service_healthy

  # Redis Commander Redis 管理工具 (可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: chatwoot-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    networks:
      - chatwoot-network
    environment:
      - REDIS_HOSTS=local:redis:6379:0:chatwoot_redis_password
      - TZ=Asia/Shanghai
    depends_on:
      redis:
        condition: service_healthy

# 网络配置
networks:
  chatwoot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  packs:
    driver: local
  node_modules:
    driver: local
  cache:
    driver: local
  bundle:
    driver: local
  rails_logs:
    driver: local
  sidekiq_logs:
    driver: local
