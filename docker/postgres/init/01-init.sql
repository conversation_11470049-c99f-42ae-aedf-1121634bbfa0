-- Chatwoot PostgreSQL 初始化脚本
-- 创建开发环境所需的数据库和扩展

-- 创建 vector 扩展 (用于 AI 功能)
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建 uuid-ossp 扩展 (用于 UUID 生成)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建 pg_trgm 扩展 (用于文本搜索)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 创建 unaccent 扩展 (用于去除重音符号)
CREATE EXTENSION IF NOT EXISTS unaccent;

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建开发用户和数据库 (如果不存在)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'chatwoot_dev') THEN
        CREATE ROLE chatwoot_dev WITH LOGIN PASSWORD 'chatwoot_dev_password';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE chatwoot_dev TO chatwoot_dev;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO chatwoot_dev;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO chatwoot_dev;

-- 创建测试数据库
CREATE DATABASE chatwoot_test OWNER postgres;
GRANT ALL PRIVILEGES ON DATABASE chatwoot_test TO postgres;
GRANT ALL PRIVILEGES ON DATABASE chatwoot_test TO chatwoot_dev;

-- 在测试数据库中也创建扩展
\c chatwoot_test;
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- 回到主数据库
\c chatwoot_dev;

-- 输出初始化完成信息
SELECT 'PostgreSQL 初始化完成，已创建必要的扩展和权限' AS status;
