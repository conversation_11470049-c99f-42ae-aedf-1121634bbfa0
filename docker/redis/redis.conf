# Redis 开发环境配置文件
# 针对 Chatwoot 开发环境优化

# 网络配置
bind 0.0.0.0
port 6379
protected-mode no

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF 持久化
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 数据库配置
databases 16

# 超时配置
timeout 0
tcp-keepalive 300

# 客户端配置
tcp-backlog 511
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 通知配置
notify-keyspace-events ""

# 哈希配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog 配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量长度
proto-max-bulk-len 512mb

# 频率配置
hz 10

# 动态频率
dynamic-hz yes

# AOF 重写增量 fsync
aof-rewrite-incremental-fsync yes

# RDB 保存增量 fsync
rdb-save-incremental-fsync yes

# LFU 和 LRU 配置
lfu-log-factor 10
lfu-decay-time 1

# 模块配置
# loadmodule /path/to/my_module.so

# 包含其他配置文件
# include /path/to/local.conf
# include /path/to/other.conf
