# Chatwoot 开发环境配置指南

本文档提供了 Chatwoot 项目的完整容器化开发环境配置指南，支持本地代码修改和实时调试。

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [详细配置](#详细配置)
- [开发工作流程](#开发工作流程)
- [常见问题](#常见问题)
- [性能优化](#性能优化)
- [故障排除](#故障排除)

## 🔧 系统要求

### 必需软件
- **Docker**: >= 20.10.0
- **Docker Compose**: >= 2.0.0
- **Git**: >= 2.30.0

### 系统资源
- **内存**: 最少 8GB，推荐 16GB
- **存储**: 最少 20GB 可用空间
- **CPU**: 最少 4 核心，推荐 8 核心

### 操作系统支持
- macOS 10.15+
- Ubuntu 20.04+
- Windows 10+ (使用 WSL2)

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/chatwoot/chatwoot.git
cd chatwoot
```

### 2. 一键启动开发环境
```bash
# 使用快速启动脚本
./start-dev.sh

# 或者使用管理脚本
./scripts/dev.sh start
```

### 3. 访问应用
启动完成后，访问以下地址：

- **主应用**: http://localhost:3000
- **Vite 开发服务器**: http://localhost:3036
- **MailHog 邮件测试**: http://localhost:8025
- **Adminer 数据库管理**: http://localhost:8080
- **Redis Commander**: http://localhost:8081

## ⚙️ 详细配置

### 环境变量配置

项目使用 `.env.development` 作为环境变量模板，启动时会自动复制为 `.env`。

主要配置项：

```bash
# 数据库配置
POSTGRES_HOST=postgres
POSTGRES_DATABASE=chatwoot_dev
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=chatwoot_dev_password

# Redis 配置
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=chatwoot_redis_password

# 邮件配置 (开发环境使用 MailHog)
SMTP_ADDRESS=mailhog
SMTP_PORT=1025
```

### 服务架构

开发环境包含以下服务：

| 服务 | 端口 | 描述 |
|------|------|------|
| rails | 3000 | Rails 主应用 |
| vite | 3036 | 前端开发服务器 |
| sidekiq | - | 后台任务处理 |
| postgres | 5432 | PostgreSQL 数据库 |
| redis | 6379 | Redis 缓存 |
| mailhog | 1025/8025 | 邮件测试服务 |
| adminer | 8080 | 数据库管理工具 |
| redis-commander | 8081 | Redis 管理工具 |

### 数据卷配置

- `postgres_data`: PostgreSQL 数据持久化
- `redis_data`: Redis 数据持久化
- `node_modules`: Node.js 依赖缓存
- `bundle`: Ruby Gem 缓存
- `packs`: 前端构建产物缓存

## 🔄 开发工作流程

### 日常开发命令

```bash
# 启动开发环境
./scripts/dev.sh start

# 查看服务状态
./scripts/dev.sh status

# 查看日志
./scripts/dev.sh logs
./scripts/dev.sh logs rails  # 查看特定服务日志

# 进入容器
./scripts/dev.sh shell rails     # 进入 Rails 容器
./scripts/dev.sh shell postgres  # 进入数据库容器

# 运行 Rails 命令
./scripts/dev.sh rails console           # Rails 控制台
./scripts/dev.sh rails generate model User  # 生成模型
./scripts/dev.sh rails routes            # 查看路由

# 数据库操作
./scripts/dev.sh db:setup     # 初始化数据库
./scripts/dev.sh db:migrate   # 运行迁移
./scripts/dev.sh db:reset     # 重置数据库

# 运行测试
./scripts/dev.sh test

# 更新依赖
./scripts/dev.sh update

# 停止服务
./scripts/dev.sh stop

# 清理资源
./scripts/dev.sh clean
```

### 代码热重载

开发环境支持以下热重载功能：

- **Ruby 代码**: 修改后自动重启 Rails 服务器
- **前端代码**: 通过 Vite 实现热模块替换 (HMR)
- **样式文件**: CSS/SCSS 修改实时更新
- **配置文件**: 环境变量修改需要重启服务

### 调试功能

1. **Rails 调试**:
   ```ruby
   # 在代码中添加断点
   binding.pry
   ```

2. **前端调试**:
   - 浏览器开发者工具
   - Vue DevTools 扩展

3. **数据库调试**:
   - 使用 Adminer: http://localhost:8080
   - 直接连接: `localhost:5432`

4. **Redis 调试**:
   - 使用 Redis Commander: http://localhost:8081
   - 命令行: `redis-cli -h localhost -p 6379`

## ❓ 常见问题

### Q: 服务启动失败怎么办？

A: 按以下步骤排查：

1. 检查 Docker 是否正常运行
2. 检查端口是否被占用
3. 查看服务日志：`./scripts/dev.sh logs`
4. 重新构建镜像：`./scripts/dev.sh build`

### Q: 数据库连接失败？

A: 检查以下配置：

1. PostgreSQL 服务是否启动
2. 环境变量配置是否正确
3. 数据库是否已初始化：`./scripts/dev.sh db:setup`

### Q: 前端资源加载失败？

A: 可能的解决方案：

1. 检查 Vite 服务是否运行
2. 清理缓存：`./scripts/dev.sh clean`
3. 重新安装依赖：`./scripts/dev.sh update`

### Q: 邮件发送测试？

A: 使用 MailHog 进行邮件测试：

1. 访问 http://localhost:8025
2. 在应用中触发邮件发送
3. 在 MailHog 界面查看邮件

## 🚀 性能优化

### 1. 资源分配优化

在 Docker Desktop 中调整资源分配：
- CPU: 4-8 核心
- 内存: 8-16GB
- 磁盘: 使用 SSD

### 2. 缓存优化

```bash
# 预热缓存
./scripts/dev.sh rails assets:precompile
./scripts/dev.sh update
```

### 3. 网络优化

使用国内镜像源（已在配置中启用）：
- Docker Hub: 阿里云镜像
- NPM: 淘宝镜像
- RubyGems: 国内镜像

## 🔧 故障排除

### 端口冲突

如果遇到端口冲突，可以修改 `docker-compose.dev.yml` 中的端口映射：

```yaml
ports:
  - "3001:3000"  # 将 3000 改为 3001
```

### 权限问题

在 Linux/macOS 上可能遇到文件权限问题：

```bash
# 修复权限
sudo chown -R $USER:$USER .
chmod +x scripts/dev.sh start-dev.sh
```

### 磁盘空间不足

定期清理 Docker 资源：

```bash
# 清理未使用的镜像和容器
./scripts/dev.sh clean

# 深度清理
docker system prune -a --volumes
```

### 服务健康检查失败

查看具体服务的健康状态：

```bash
# 查看所有服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看特定服务日志
./scripts/dev.sh logs postgres
./scripts/dev.sh logs redis
```

## 📞 获取帮助

- **官方文档**: https://developers.chatwoot.com
- **GitHub Issues**: https://github.com/chatwoot/chatwoot/issues
- **社区论坛**: https://github.com/chatwoot/chatwoot/discussions

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

---

**注意**: 此开发环境仅用于本地开发，不适用于生产环境部署。
