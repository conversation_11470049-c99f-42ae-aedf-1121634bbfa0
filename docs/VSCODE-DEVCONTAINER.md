# VS Code Dev Container 配置指南

本文档说明如何使用 VS Code 的 Dev Container 功能进行 Chatwoot 开发。

## 🎯 概述

VS Code Dev Container 允许您在容器化环境中进行开发，提供一致的开发体验。我们已经为 Chatwoot 项目配置了完整的 Dev Container 环境。

## 📋 前提条件

1. **VS Code**: 安装最新版本的 Visual Studio Code
2. **Dev Containers 扩展**: 安装 `ms-vscode-remote.remote-containers` 扩展
3. **Docker**: 确保 Docker 正在运行

## 🚀 快速开始

### 1. 打开项目
```bash
# 克隆项目
git clone https://github.com/chatwoot/chatwoot.git
cd chatwoot

# 使用 VS Code 打开
code .
```

### 2. 在容器中重新打开
当 VS Code 检测到 `.devcontainer` 配置时，会提示您在容器中重新打开项目：

1. 点击右下角的通知 "Reopen in Container"
2. 或者使用命令面板 (`Cmd+Shift+P`) 搜索 "Dev Containers: Reopen in Container"

### 3. 等待容器构建
首次打开时，VS Code 会：
- 构建开发容器
- 安装必要的扩展
- 运行初始化脚本
- 设置开发环境

## ⚙️ 配置详情

### Dev Container 配置
我们的 `.devcontainer/devcontainer.json` 配置包含：

```json
{
  "name": "Chatwoot Development Environment",
  "dockerComposeFile": ["../docker-compose.dev.yml"],
  "service": "rails",
  "workspaceFolder": "/app"
}
```

### 主要特性

#### 🔧 自动安装的扩展
- **Shopify.ruby-lsp**: Ruby 语言服务器
- **misogi.ruby-rubocop**: Ruby 代码格式化
- **Vue.volar**: Vue.js 支持
- **GitHub.copilot**: AI 代码助手
- **bradlc.vscode-tailwindcss**: Tailwind CSS 支持

#### 🌐 端口转发
自动转发以下端口到本地：
- `3000`: Rails 主应用
- `3036`: Vite 开发服务器
- `5432`: PostgreSQL 数据库
- `6379`: Redis 缓存
- `8025`: MailHog 邮件测试
- `8080`: Adminer 数据库管理
- `8081`: Redis Commander

#### 📁 工作区设置
- 工作目录: `/app`
- 代码挂载: 支持实时编辑
- 环境变量: 自动加载 `.env.development`

## 🛠️ 开发工作流

### 启动开发服务器
容器启动后，在 VS Code 终端中运行：

```bash
# 启动 Rails 服务器
bundle exec rails server

# 在新终端中启动 Vite
bin/vite dev

# 启动 Sidekiq (可选)
bundle exec sidekiq
```

### 常用命令
```bash
# Rails 控制台
bundle exec rails console

# 运行测试
bundle exec rspec

# 数据库迁移
bundle exec rails db:migrate

# 安装依赖
bundle install
pnpm install
```

### 调试功能
1. **Ruby 调试**: 在代码中添加 `binding.pry`
2. **前端调试**: 使用浏览器开发者工具
3. **数据库调试**: 访问 http://localhost:8080 (Adminer)

## 🔧 自定义配置

### 修改 VS Code 设置
编辑 `.vscode/settings.json` 来自定义编辑器设置：

```json
{
  "editor.formatOnSave": true,
  "ruby.format": "rubocop",
  "editor.tabSize": 2
}
```

### 添加扩展
在 `.devcontainer/devcontainer.json` 的 `extensions` 数组中添加扩展 ID：

```json
"extensions": [
  "Shopify.ruby-lsp",
  "your-extension-id"
]
```

### 修改端口转发
在 `devcontainer.json` 中修改 `forwardPorts` 配置：

```json
"forwardPorts": [3000, 3036, 8025],
"portsAttributes": {
  "3000": {
    "label": "Rails Server",
    "onAutoForward": "notify"
  }
}
```

## 🐛 故障排除

### 容器构建失败
1. 检查 Docker 是否正在运行
2. 清理 Docker 缓存: `docker system prune`
3. 重新构建容器: "Dev Containers: Rebuild Container"

### 端口冲突
如果端口被占用：
1. 修改 `docker-compose.dev.yml` 中的端口映射
2. 重新启动容器

### 扩展未安装
1. 检查网络连接
2. 手动安装扩展: `Ctrl+Shift+X`
3. 重新构建容器

### 数据库连接问题
1. 检查 PostgreSQL 服务状态
2. 验证环境变量配置
3. 运行数据库初始化: `bundle exec rails db:setup`

## 📚 高级功能

### 多服务调试
可以同时调试多个服务：
1. Rails 应用调试
2. Sidekiq 后台任务调试
3. 前端 JavaScript 调试

### Git 集成
- 自动配置 Git 安全目录
- 支持 GitHub CLI
- 集成 Pull Request 管理

### 性能优化
- 使用 Docker 卷缓存依赖
- 预编译常用资源
- 优化文件监视排除

## 🤝 贡献指南

如果您需要修改 Dev Container 配置：

1. 编辑 `.devcontainer/devcontainer.json`
2. 测试配置更改
3. 提交 Pull Request

## 📞 获取帮助

- **VS Code Dev Containers 文档**: https://code.visualstudio.com/docs/remote/containers
- **Chatwoot 开发文档**: [docs/DEVELOPMENT.md](DEVELOPMENT.md)
- **问题反馈**: GitHub Issues

---

**注意**: Dev Container 环境与本地 Docker Compose 环境是兼容的，您可以根据需要选择使用。
