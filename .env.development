# Chatwoot 开发环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基础配置
# =============================================================================
RAILS_ENV=development
NODE_ENV=development
RACK_ENV=development

# 应用基础配置
FRONTEND_URL=http://localhost:3000
FORCE_SSL=false
SECRET_KEY_BASE=replace_with_your_secret_key_base_in_production

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DATABASE=chatwoot_dev
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=chatwoot_dev_password

# 数据库连接池配置
RAILS_MAX_THREADS=5
SIDEKIQ_CONCURRENCY=10
DB_POOL_REAPING_FREQUENCY=30
POSTGRES_STATEMENT_TIMEOUT=14s

# =============================================================================
# Redis 配置
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=chatwoot_redis_password
REDIS_SENTINELS=
REDIS_SENTINEL_MASTER_NAME=

# =============================================================================
# 邮件服务配置 (开发环境使用 MailHog)
# =============================================================================
MAILER_SENDER_EMAIL=<EMAIL>
SMTP_DOMAIN=chatwoot.dev
SMTP_ADDRESS=mailhog
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_AUTHENTICATION=
SMTP_ENABLE_STARTTLS_AUTO=false
SMTP_TLS=false
SMTP_OPENSSL_VERIFY_MODE=none

# =============================================================================
# 文件存储配置
# =============================================================================
ACTIVE_STORAGE_SERVICE=local
# 如果使用云存储，取消注释并配置以下选项
# ACTIVE_STORAGE_SERVICE=amazon
# S3_BUCKET_NAME=
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=

# =============================================================================
# 日志配置
# =============================================================================
RAILS_LOG_TO_STDOUT=true
LOG_LEVEL=debug
RAILS_LOG_LEVEL=debug

# =============================================================================
# 开发工具配置
# =============================================================================
# Vite 开发服务器配置
VITE_DEV_SERVER_HOST=vite
VITE_DEV_SERVER_PORT=3036

# 调试配置
ENABLE_DEBUG_LOGS=true
BULLET_ENABLED=true

# =============================================================================
# 安全配置 (开发环境)
# =============================================================================
# JWT 配置
JWT_SECRET_KEY=your_jwt_secret_key_for_development

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3036

# =============================================================================
# 第三方服务配置 (可选)
# =============================================================================
# 如果需要集成第三方服务，取消注释并配置

# Google OAuth (可选)
# GOOGLE_OAUTH_CLIENT_ID=
# GOOGLE_OAUTH_CLIENT_SECRET=
# GOOGLE_OAUTH_CALLBACK_URL=http://localhost:3000/omniauth/google_oauth2/callback

# Facebook OAuth (可选)
# FB_APP_ID=
# FB_APP_SECRET=
# FB_VERIFY_TOKEN=

# Slack 集成 (可选)
# SLACK_CLIENT_ID=
# SLACK_CLIENT_SECRET=

# Twilio 集成 (可选)
# TWILIO_ACCOUNT_SID=
# TWILIO_AUTH_TOKEN=
# TWILIO_PHONE_NUMBER=

# =============================================================================
# 性能监控配置 (可选)
# =============================================================================
# New Relic (可选)
# NEW_RELIC_LICENSE_KEY=
# NEW_RELIC_APP_NAME=chatwoot-development

# Sentry 错误监控 (可选)
# SENTRY_DSN=

# =============================================================================
# 功能开关配置
# =============================================================================
# 启用/禁用特定功能
ENABLE_ACCOUNT_SIGNUP=true
ENABLE_EMAIL_CHANNEL=true
ENABLE_SMS_CHANNEL=true
ENABLE_WEBSITE_CHANNEL=true
ENABLE_FB_CHANNEL=true
ENABLE_TWITTER_CHANNEL=true
ENABLE_WHATSAPP_CHANNEL=true
ENABLE_LINE_CHANNEL=true
ENABLE_TELEGRAM_CHANNEL=true

# =============================================================================
# 开发环境特定配置
# =============================================================================
# 种子数据配置
CREATE_SAMPLE_ACCOUNT_DATA=true
SAMPLE_ACCOUNT_NAME="开发测试账户"
SAMPLE_ACCOUNT_EMAIL="<EMAIL>"
SAMPLE_ACCOUNT_PASSWORD="chatwoot123"

# 开发服务器配置
RAILS_SERVE_STATIC_FILES=false
WEBPACKER_DEV_SERVER_HOST=0.0.0.0
WEBPACKER_DEV_SERVER_PORT=3035

# 缓存配置
RAILS_CACHE_STORE=redis_cache_store

# =============================================================================
# 国际化配置
# =============================================================================
DEFAULT_LOCALE=zh_CN
AVAILABLE_LOCALES=en,zh_CN,zh_TW

# =============================================================================
# 其他配置
# =============================================================================
# 时区配置
TZ=Asia/Shanghai

# 应用版本
CHATWOOT_VERSION=development

# API 配置
API_RATE_LIMIT=100
API_RATE_LIMIT_PERIOD=1.minute

# WebSocket 配置
ACTION_CABLE_ALLOWED_REQUEST_ORIGINS=http://localhost:3000,http://localhost:3036

# 文件上传限制
MAX_FILE_SIZE_MB=40
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/bmp,image/tiff,image/svg+xml,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,text/plain,text/csv,application/json,application/zip,audio/mpeg,audio/wav,video/mp4,video/quicktime

# 头像配置
AVATAR_MAX_SIZE_MB=10

# 品牌配置
BRAND_NAME=Chatwoot
BRAND_URL=https://chatwoot.com
TERMS_URL=
PRIVACY_URL=
SUPPORT_EMAIL=<EMAIL>

# 安装配置
INSTALLATION_NAME=chatwoot-development
INSTALLATION_VERSION=development
INSTALLATION_HOST=localhost:3000
