# This file was automatically generated by `qlty init`.
# You can modify it to suit your needs.
# We recommend you to commit this file to your repository.
#
# This configuration is used by both Qlty CLI and Qlty Cloud.
#
#     Qlty CLI -- Code quality toolkit for developers
#     Qlty Cloud -- Fully automated Code Health Platform
#
# Try Qlty Cloud: https://qlty.sh
#
# For a guide to configuration, visit https://qlty.sh/d/config
# Or for a full reference, visit https://qlty.sh/d/qlty-toml
config_version = "0"

exclude_patterns = [
  "*_min.*",
  "*-min.*",
  "*.min.*",
  "**/.yarn/**",
  "**/*.d.ts",
  "**/assets/**",
  "**/bower_components/**",
  "**/build/**",
  "**/cache/**",
  "**/config/**",
  "**/db/**",
  "**/deps/**",
  "**/dist/**",
  "**/extern/**",
  "**/external/**",
  "**/generated/**",
  "**/Godeps/**",
  "**/gradlew/**",
  "**/mvnw/**",
  "**/node_modules/**",
  "**/protos/**",
  "**/seed/**",
  "**/target/**",
  "**/templates/**",
  "**/testdata/**",
  "**/vendor/**", "spec/", "**/specs/**/**", "**/spec/**/**", "db/*", "bin/**/*", "db/**/*", "config/**/*", "public/**/*", "vendor/**/*", "node_modules/**/*", "lib/tasks/auto_annotate_models.rake", "app/test-matchers.js", "docs/*", "**/*.md", "**/*.yml", "app/javascript/dashboard/i18n/locale", "**/*.stories.js", "stories/", "app/javascript/dashboard/components/widgets/conversation/advancedFilterItems/index.js", "app/javascript/shared/constants/countries.js", "app/javascript/dashboard/components/widgets/conversation/advancedFilterItems/languages.js", "app/javascript/dashboard/routes/dashboard/contacts/contactFilterItems/index.js", "app/javascript/dashboard/routes/dashboard/settings/automation/constants.js", "app/javascript/dashboard/components/widgets/FilterInput/FilterOperatorTypes.js", "app/javascript/dashboard/routes/dashboard/settings/reports/constants.js", "app/javascript/dashboard/store/captain/storeFactory.js", "app/javascript/dashboard/i18n/index.js", "app/javascript/widget/i18n/index.js", "app/javascript/survey/i18n/index.js", "app/javascript/shared/constants/locales.js", "app/javascript/dashboard/helper/specs/macrosFixtures.js", "app/javascript/dashboard/routes/dashboard/settings/macros/constants.js", "**/fixtures/**", "**/*/fixtures.js",
]

test_patterns = [
  "**/test/**",
  "**/spec/**",
  "**/*.test.*",
  "**/*.spec.*",
  "**/*_test.*",
  "**/*_spec.*",
  "**/test_*.*",
  "**/spec_*.*",
]

[smells]
mode = "comment"

[smells.boolean_logic]
threshold = 4

[smells.file_complexity]
threshold = 66
enabled = true

[smells.return_statements]
threshold = 4

[smells.nested_control_flow]
threshold = 4

[smells.function_parameters]
threshold = 4

[smells.function_complexity]
threshold = 5

[smells.duplication]
enabled = true
threshold = 20

[[source]]
name = "default"
default = true
