#!/bin/bash

# VS Code Dev Container 启动后执行脚本
# 用于启动必要的开发服务

set -e

echo "🔄 启动 Chatwoot 开发服务..."

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 清理可能存在的 PID 文件
log_info "清理临时文件..."
rm -rf /app/tmp/pids/server.pid
rm -rf /app/tmp/cache/*

# 检查数据库连接
log_info "检查数据库连接..."
if bundle exec rails runner "ActiveRecord::Base.connection" > /dev/null 2>&1; then
    log_success "数据库连接正常"
else
    log_warning "数据库连接失败，请检查配置"
fi

# 检查 Redis 连接
log_info "检查 Redis 连接..."
if redis-cli -h redis -p 6379 ping > /dev/null 2>&1; then
    log_success "Redis 连接正常"
else
    log_warning "Redis 连接失败，请检查配置"
fi

# 更新依赖（如果需要）
if [ -f "/app/Gemfile.lock" ] && [ -f "/app/pnpm-lock.yaml" ]; then
    log_info "检查依赖更新..."
    
    # 检查 Gemfile 是否有更新
    if [ "/app/Gemfile" -nt "/app/Gemfile.lock" ]; then
        log_info "检测到 Gemfile 更新，重新安装 Ruby 依赖..."
        bundle install
    fi
    
    # 检查 package.json 是否有更新
    if [ "/app/package.json" -nt "/app/pnpm-lock.yaml" ]; then
        log_info "检测到 package.json 更新，重新安装 Node.js 依赖..."
        pnpm install
    fi
fi

# 运行数据库迁移（如果有新的迁移）
log_info "检查数据库迁移..."
if bundle exec rails db:migrate:status | grep -q "down"; then
    log_info "发现新的数据库迁移，正在执行..."
    bundle exec rails db:migrate
    log_success "数据库迁移完成"
fi

log_success "🎉 开发服务启动完成！"
echo ""
echo "💡 提示："
echo "  - 使用 Ctrl+Shift+\` 打开集成终端"
echo "  - 使用 F5 启动调试模式"
echo "  - 修改代码后服务会自动重载"
echo ""
echo "🚀 开始开发吧！"
