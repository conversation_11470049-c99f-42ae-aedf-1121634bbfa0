#!/bin/bash

# VS Code Dev Container 创建后执行脚本
# 用于初始化开发环境

set -e

echo "🚀 初始化 Chatwoot 开发容器..."

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境文件
if [ ! -f "/app/.env" ]; then
    log_info "创建环境配置文件..."
    cp /app/.env.development /app/.env
    log_success "环境配置文件已创建"
fi

# 等待数据库服务启动
log_info "等待 PostgreSQL 服务启动..."
until pg_isready -h postgres -p 5432 -U postgres; do
    echo "等待数据库连接..."
    sleep 2
done
log_success "PostgreSQL 服务已就绪"

# 等待 Redis 服务启动
log_info "等待 Redis 服务启动..."
until redis-cli -h redis -p 6379 ping; do
    echo "等待 Redis 连接..."
    sleep 2
done
log_success "Redis 服务已就绪"

# 安装 Ruby 依赖
log_info "安装 Ruby 依赖..."
bundle install
log_success "Ruby 依赖安装完成"

# 安装 Node.js 依赖
log_info "安装 Node.js 依赖..."
pnpm install
log_success "Node.js 依赖安装完成"

# 设置数据库
log_info "设置数据库..."
if bundle exec rails db:version > /dev/null 2>&1; then
    log_info "数据库已存在，运行迁移..."
    bundle exec rails db:migrate
else
    log_info "创建并初始化数据库..."
    bundle exec rails db:create
    bundle exec rails db:migrate
    bundle exec rails db:seed
fi
log_success "数据库设置完成"

# 预编译资源（开发环境可选）
log_info "预编译开发资源..."
bundle exec rails assets:precompile RAILS_ENV=development || log_warning "资源预编译失败，但不影响开发"

# 设置 Git 配置（如果需要）
if [ ! -f "/app/.git/config" ]; then
    log_info "初始化 Git 配置..."
    git config --global --add safe.directory /app
fi

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p /app/tmp/pids
mkdir -p /app/tmp/cache
mkdir -p /app/log
mkdir -p /app/storage

# 设置权限
log_info "设置文件权限..."
chmod +x /app/scripts/dev.sh
chmod +x /app/start-dev.sh

log_success "🎉 开发容器初始化完成！"
echo ""
echo "📱 可用服务："
echo "  🌐 Rails 应用:        http://localhost:3000"
echo "  ⚡ Vite 开发服务器:    http://localhost:3036"
echo "  📧 MailHog 邮件测试:   http://localhost:8025"
echo "  🗄️ Adminer 数据库:     http://localhost:8080"
echo "  🔴 Redis Commander:   http://localhost:8081"
echo ""
echo "🛠️ 常用命令："
echo "  启动开发服务器:  bundle exec rails server"
echo "  启动 Vite:      bin/vite dev"
echo "  运行测试:       bundle exec rspec"
echo "  Rails 控制台:   bundle exec rails console"
echo ""
echo "📚 更多信息请查看: docs/DEVELOPMENT.md"
