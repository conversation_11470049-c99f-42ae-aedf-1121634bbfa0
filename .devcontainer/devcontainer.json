{
  "name": "Chatwoot Development Environment",
  "dockerComposeFile": [
    "../docker-compose.dev.yml"
  ],
  "service": "rails",
  "workspaceFolder": "/app",

  // VS Code 设置
  "customizations": {
    "vscode": {
      "settings": {
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "/bin/bash"
          },
          "zsh": {
            "path": "/bin/zsh"
          }
        },
        "extensions.ignoreRecommendations": false,
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.fixAll.rubocop": "explicit"
        },
        "files.trimTrailingWhitespace": true,
        "files.insertFinalNewline": true,
        "files.associations": {
          "*.html.erb": "erb",
          "*.rb": "ruby",
          "Gemfile": "ruby",
          "Rakefile": "ruby"
        },
        "search.exclude": {
          "**/node_modules": true,
          "**/tmp": true,
          "**/log": true,
          "**/coverage": true,
          "**/public/packs": true,
          "**/public/vite": true,
          "**/vendor": true,
          "**/.git": true
        },
        "ruby.intellisense": "rubyLocate",
        "ruby.format": "rubocop"
      },

      // VS Code 扩展
      "extensions": [
        "Shopify.ruby-lsp",
        "misogi.ruby-rubocop",
        "davidpallinder.rails-test-runner",
        "github.copilot",
        "mrmlnc.vscode-duplicate",
        "bradlc.vscode-tailwindcss",
        "Vue.volar",
        "ms-vscode.vscode-typescript-next",
        "esbenp.prettier-vscode",
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode-remote.remote-containers",
        "GitHub.vscode-pull-request-github"
      ]
    }
  },

  // 端口转发配置
  "forwardPorts": [3000, 3036, 5432, 6379, 8025, 8080, 8081],
  "portsAttributes": {
    "3000": {
      "label": "Rails 主应用",
      "onAutoForward": "notify"
    },
    "3036": {
      "label": "Vite 开发服务器",
      "onAutoForward": "notify"
    },
    "5432": {
      "label": "PostgreSQL 数据库",
      "onAutoForward": "silent"
    },
    "6379": {
      "label": "Redis 缓存",
      "onAutoForward": "silent"
    },
    "8025": {
      "label": "MailHog 邮件测试",
      "onAutoForward": "notify"
    },
    "8080": {
      "label": "Adminer 数据库管理",
      "onAutoForward": "notify"
    },
    "8081": {
      "label": "Redis Commander",
      "onAutoForward": "notify"
    }
  },

  // 容器创建后执行的命令
  "postCreateCommand": "bash .devcontainer/scripts/post-create.sh",

  // 容器启动后执行的命令
  "postStartCommand": "bash .devcontainer/scripts/post-start.sh",

  // 挂载配置
  "mounts": [
    "source=${localWorkspaceFolder}/.env.development,target=/app/.env,type=bind,consistency=cached"
  ],

  // 功能配置
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },

  // 远程用户配置
  "remoteUser": "root",

  // 关闭时保持容器运行
  "shutdownAction": "none"
}
