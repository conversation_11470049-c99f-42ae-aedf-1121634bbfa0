{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "إلغاء", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "خيارات الربط", "DESCRIPTION": "Chatwoot تتكامل مع أدوات وخدمات متعددة لتحسين كفاءة فريقك. استكشف القائمة أدناه لتكوين تطبيقاتك المفضلة.", "LEARN_MORE": "معرفة المزيد عن التكاملات", "LOADING": "جاري جلب التكاملات", "CAPTAIN": {"DISABLED": "لم يتم تمكين الكابتن على حسابك.", "CLICK_HERE_TO_CONFIGURE": "انقر هنا للتهيئة", "LOADING_CONSOLE": "جار جلب وحدة التحكم بالكابتن...", "FAILED_TO_LOAD_CONSOLE": "فشل أثناء جلب وحدة تحكم الكابتن. الرجاء التحديث والمحاولة مرة أخرى."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "الأحداث المشتركة", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "إلغاء", "DESC": "أحداث Webhook توفر لك معلومات في الوقت الحقيقي حول ما يحدث في حساب Chatwoot الخاص بك. الرجاء إدخال عنوان URL صالح لتكوين callback.", "SUBSCRIPTIONS": {"LABEL": "الأحداث", "EVENTS": {"CONVERSATION_CREATED": "تم إنشاء المحادثة", "CONVERSATION_STATUS_CHANGED": "تم تغيير حالة المحادثة", "CONVERSATION_UPDATED": "تم تحديث المحادثة", "MESSAGE_CREATED": "تم إنشاء رسالة", "MESSAGE_UPDATED": "تم تحديث الرسالة", "WEBWIDGET_TRIGGERED": "أداة الدردشة المباشرة مفتوحة من قبل المستخدم", "CONTACT_CREATED": "Contact created", "CONTACT_UPDATED": "Contact updated", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "رابط Webhook", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "الرجاء إدخال عنوان URL صالح"}, "EDIT_SUBMIT": "تحديث الويبهوك", "ADD_SUBMIT": "إنشاء webhook"}, "TITLE": "Webhook", "CONFIGURE": "تهيئة", "HEADER": "إعدادات الـ Webhook", "HEADER_BTN_TXT": "إضافة webhook جديد", "LOADING": "جار جلب الـ Webhooks", "SEARCH_404": "لا توجد عناصر مطابقة لهذا الاستعلام", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhooks هي إعادات HTTP callbacks التي يمكن تعريفها لكل حساب. يتم تشغيلها بأحداث مثل إنشاء الرسائل في Chatwoot. يمكنك إنشاء أكثر من Webhook واحد لهذا الحساب. <br /><br /> لإنشاء <b>webhook</b>، انقر فوق <b>إضافة Webhooks جديد</b>. يمكنك أيضا إزالة أي رابط ويب موجود بالنقر على زر الحذف.</p>", "LIST": {"404": "لا توجد webhooks مكونة لهذا الحساب.", "TITLE": "إدارة الـ webhooks", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook endpoint", "ACTIONS": "الإجراءات"}}, "EDIT": {"BUTTON_TEXT": "تعديل", "TITLE": "تعديل webhook", "API": {"SUCCESS_MESSAGE": "تم تحديث تكوين ويبهوك بنجاح", "ERROR_MESSAGE": "تعذر الاتصال بالخادم، الرجاء المحاولة مرة أخرى لاحقاً"}}, "ADD": {"CANCEL": "إلغاء", "TITLE": "إضافة webhook جديد", "API": {"SUCCESS_MESSAGE": "تم إضافة إعدادات Webhook بنجاح", "ERROR_MESSAGE": "تعذر الاتصال بالخادم، الرجاء المحاولة مرة أخرى لاحقاً"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "تم حذف <PERSON> بنجاح", "ERROR_MESSAGE": "تعذر الاتصال بالخادم، الرجاء المحاولة مرة أخرى لاحقاً"}, "CONFIRM": {"TITLE": "تأكيد الحذف", "MESSAGE": "هل أنت متأكد من حذف webhook؟ ({webhookURL})", "YES": "نعم، احذف ", "NO": "لا، احتفظ به"}}}, "SLACK": {"DELETE": "<PERSON><PERSON><PERSON>", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "استخدام تكامل Slack", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "تحديث", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with Chatwoot. However, the integration is currently inactive. To activate the integration and connect a channel to Chatwoot, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the Chatwoot app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Click here to join", "LEAVE_THE_ROOM": "Leave the room", "START_VIDEO_CALL_HELP_TEXT": "Start a new video call with the customer", "JOIN_ERROR": "There was an error joining the call, please try again", "CREATE_ERROR": "There was an error creating a meeting link, please try again"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "تبسيط"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "إلغاء"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "تحتاج مساعدة؟", "DISMISS": "تجاهل", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Improve With AI", "SUMMARY_TITLE": "Summary with AI", "REPLY_TITLE": "Reply suggestion with AI", "SUBTITLE": "An improved reply will be generated using AI, based on your current draft.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Professional", "FRIENDLY": "Friendly"}}, "BUTTONS": {"GENERATE": "Generate", "GENERATING": "Generating...", "CANCEL": "إلغاء"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "تم حذف التكامل بنجاح"}}, "CONNECT": {"BUTTON_TEXT": "ربط الاتصال"}, "DASHBOARD_APPS": {"TITLE": "تطبيقات لوحة التحكم", "HEADER_BTN_TXT": "إضافة تطبيق جديد للوحة التحكم", "SIDEBAR_TXT": "<p><b>Dashboard Apps</b></p><p>Dashboard Apps allow organizations to embed an application inside the Chatwoot dashboard to provide the context for customer support agents. This feature allows you to create an application independently and embed that inside the dashboard to provide user information, their orders, or their previous payment history.</p><p>When you embed your application using the dashboard in Chatwoot, your application will get the context of the conversation and contact as a window event. Implement a listener for the message event on your page to receive the context.</p><p>To add a new dashboard app, click on the button 'Add a new dashboard app'.</p>", "DESCRIPTION": "تسمح تطبيقات لوحة التحكم للمنظمات بتضمين تطبيق داخل لوحة التحكم لتوفير السياق لوكلاء دعم العملاء. هذه الميزة تسمح لك بإنشاء تطبيق بشكل مستقل وإدراج لتوفير معلومات المستخدم أو طلباتهم أو سجل الدفع السابق.", "LEARN_MORE": "معرفة المزيد حول تطبيقات لوحة التحكم", "LIST": {"404": "لا توجد تطبيقات لوحة التحكم التي تم تكوينها على هذا الحساب حتى الآن", "LOADING": "جلب تطبيقات لوحة التحكم...", "TABLE_HEADER": {"NAME": "الاسم", "ENDPOINT": "نقطة الوصول"}, "EDIT_TOOLTIP": "تعديل التطبيق", "DELETE_TOOLTIP": "<PERSON><PERSON><PERSON> التطبيق"}, "FORM": {"TITLE_LABEL": "الاسم", "TITLE_PLACEHOLDER": "أدخل اسم لتطبيق لوحة التحكم الخاصة بك", "TITLE_ERROR": "مطلوب اسم لتطبيق لوحة التحكم", "URL_LABEL": "نقطة الوصول", "URL_PLACEHOLDER": "أدخل عنوان URL لنقطة النهاية حيث يتم استضافة التطبيق الخاص بك", "URL_ERROR": "رابط صحيح مطلوب"}, "CREATE": {"HEADER": "إضافة تطبيق جديد للوحة التحكم", "FORM_SUBMIT": "إرسال", "FORM_CANCEL": "إلغاء", "API_SUCCESS": "تم تكوين تطبيق لوحة التحكم بنجاح", "API_ERROR": "لم نتمكن من إنشاء تطبيق. الرجاء المحاولة مرة أخرى لاحقاً"}, "UPDATE": {"HEADER": "تعديل تطبيق لوحة التحكم", "FORM_SUBMIT": "تحديث", "FORM_CANCEL": "إلغاء", "API_SUCCESS": "تم تحديث تطبيق لوحة التحكم بنجاح", "API_ERROR": "لم نتمكن من تحديث التطبيق. الرجاء المحاولة مرة أخرى لاحقاً"}, "DELETE": {"CONFIRM_YES": "نعم، احذف", "CONFIRM_NO": "لا، احتفظ به", "TITLE": "تأكيد الحذف", "MESSAGE": "هل أنت متأكد من حذف التطبيق - {appName}؟", "API_SUCCESS": "تم حذف تطبيق لوحة التحكم بنجاح", "API_ERROR": "لم نتمكن من حذف التطبيق. الرجاء المحاولة مرة أخرى لاحقاً"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "جلب مشاكل من Linear...", "LOADING_ERROR": "حد<PERSON> خطأ أثناء جلب المشكلات من Linear، الرجاء المحاولة مرة أخرى", "CREATE": "إنشاء", "LINK": {"SEARCH": "البحث عن المشكلات", "SELECT": "اختر مشكلة", "TITLE": "الرابط", "EMPTY_LIST": "لم يتم العثور على مشاكل في Linear", "LOADING": "جار التحميل", "ERROR": "حد<PERSON> خطأ أثناء جلب المشكلات من Linear، الرجاء المحاولة مرة أخرى", "LINK_SUCCESS": "تم ربط المشكلة بنجاح", "LINK_ERROR": "حد<PERSON> خطأ أثناء ربط المشكلة، الرجاء المحاولة مرة أخرى", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "إنشاء/رابط مشكلة في Linear", "DESCRIPTION": "إنشاء مشكلات في Linear من المحادثات، أو ربط المشكلات الموجودة لتعقب سلس.", "FORM": {"TITLE": {"LABEL": "العنوان", "PLACEHOLDER": "أ<PERSON><PERSON><PERSON> العنوان", "REQUIRED_ERROR": "العنوان مطلوب"}, "DESCRIPTION": {"LABEL": "الوصف", "PLACEHOLDER": "أد<PERSON>ل الوصف"}, "TEAM": {"LABEL": "الفريق", "PLACEHOLDER": "اختر فريق", "SEARCH": "البحث عن فريق", "REQUIRED_ERROR": "الفريق مطلوب"}, "ASSIGNEE": {"LABEL": "المكلَّف", "PLACEHOLDER": "اختر المحال إليه", "SEARCH": "البحث عن المحال إليه"}, "PRIORITY": {"LABEL": "الأولوية", "PLACEHOLDER": "تحديد الأولوية", "SEARCH": "أولوية البحث"}, "LABEL": {"LABEL": "الوسم", "PLACEHOLDER": "<PERSON><PERSON><PERSON> التسمية", "SEARCH": "ابحث عن تصنيفات"}, "STATUS": {"LABEL": "الحالة", "PLACEHOLDER": "اختر الحالة", "SEARCH": "حالة البحث"}, "PROJECT": {"LABEL": "المشروع", "PLACEHOLDER": "<PERSON><PERSON><PERSON> المشروع", "SEARCH": "البحث عن المشروع"}}, "CREATE": "إنشاء", "CANCEL": "إلغاء", "CREATE_SUCCESS": "تم إنشاء المشكلة بنجاح", "CREATE_ERROR": "حد<PERSON> خطأ أثناء إنشاء المشكلة، يرجى المحاولة مرة أخرى", "LOADING_TEAM_ERROR": "حد<PERSON> خطأ أثناء جلب الفرق, الرجاء المحاولة مرة أخرى", "LOADING_TEAM_ENTITIES_ERROR": "حد<PERSON> خطأ أثناء جلب كيانات الفريق، يرجى المحاولة مرة أخرى"}, "ISSUE": {"STATUS": "الحالة", "PRIORITY": "الأولوية", "ASSIGNEE": "المكلَّف", "LABELS": "الوسوم", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "إلغاء الربط", "SUCCESS": "تم إلغاء ربط المشكلة بنجاح", "ERROR": "حد<PERSON> خطأ أثناء إلغاء ربط المشكلة، الرجاء المحاولة مرة أخرى"}, "NO_LINKED_ISSUES": "No linked issues found", "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "نعم، احذف", "CANCEL": "إلغاء"}, "CTA": {"TITLE": "Connect to Linear", "AGENT_DESCRIPTION": "Linear workspace is not connected. Request your administrator to connect a workspace to use this integration.", "DESCRIPTION": "Linear workspace is not connected. Click the button below to connect your workspace to use this integration.", "BUTTON_TEXT": "Connect Linear workspace"}}, "NOTION": {"DELETE": {"TITLE": "Are you sure you want to delete the Notion integration?", "MESSAGE": "Deleting this integration will remove access to your Notion workspace and stop all related functionality.", "CONFIRM": "نعم، احذف", "CANCEL": "إلغاء"}}}, "CAPTAIN": {"NAME": "قائد", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"TITLE": "Copilot", "TRY_THESE_PROMPTS": "Try these prompts", "PANEL_TITLE": "Get started with Copilot", "KICK_OFF_MESSAGE": "Need a quick summary, want to check past conversations, or draft a better reply? <PERSON><PERSON><PERSON>’s here to speed things up.", "SEND_MESSAGE": "إرسال الرسالة...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "أنت", "USE": "Use this", "RESET": "Reset", "SHOW_STEPS": "Show steps", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}, "HIGH_PRIORITY": {"LABEL": "High priority conversations", "CONTENT": "Give me a summary of all high priority open conversations. Include the conversation ID, customer name (if available), last message content, and assigned agent. Group by status if relevant."}, "LIST_CONTACTS": {"LABEL": "List contacts", "CONTENT": "Show me the list of top 10 contacts. Include name, email or phone number (if available), last seen time, tags (if any)."}}}, "PLAYGROUND": {"USER": "أنت", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "أكتب رسالتك...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "إلغاء", "CREATE": "إنشاء", "EDIT": "تحديث"}, "ASSISTANTS": {"HEADER": "Assistants", "NO_ASSISTANTS_AVAILABLE": "There are no assistants available in your account.", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "نعم، احذف", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "تحديث", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "الخصائص", "TOOLS": "Tools "}, "NAME": {"LABEL": "الاسم", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "TEMPERATURE": {"LABEL": "Response Temperature", "DESCRIPTION": "Adjust how creative or restrictive the assistant's responses should be. Lower values produce more focused and deterministic responses, while higher values allow for more creative and varied outputs."}, "DESCRIPTION": {"LABEL": "الوصف", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "الخصائص", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions.", "ALLOW_CITATIONS": "Include source citations in responses"}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "SETTINGS": {"BREADCRUMB": {"ASSISTANT": "Assistant"}, "BASIC_SETTINGS": {"TITLE": "Basic settings", "DESCRIPTION": "Customize what the assistant says when ending a conversation or transferring to a human."}, "SYSTEM_SETTINGS": {"TITLE": "System settings", "DESCRIPTION": "Customize what the assistant says when ending a conversation or transferring to a human."}, "CONTROL_ITEMS": {"TITLE": "The Fun Stuff", "DESCRIPTION": "Add more control to the assistant. (a bit more visual like a story : Query guardrail → scenarios → output) Nudges user to actually utilise these.", "OPTIONS": {"GUARDRAILS": {"TITLE": "Guardrails", "DESCRIPTION": "Keeps things on track—only the kinds of questions you want your assistant to answer, nothing off-limits or off-topic."}, "SCENARIOS": {"TITLE": "Scenarios", "DESCRIPTION": "Give your assistant some context—like “what to do when a user is stuck,” or “how to act during a refund request.”"}, "RESPONSE_GUIDELINES": {"TITLE": "Response guidelines", "DESCRIPTION": "The vibe and structure of your assistant’s replies—clear and friendly? Short and snappy? Detailed and formal?"}}}}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}, "GUARDRAILS": {"TITLE": "Guardrails", "DESCRIPTION": "Keeps things on track—only the kinds of questions you want your assistant to answer, nothing off-limits or off-topic.", "BREADCRUMB": {"TITLE": "Guardrails"}, "BULK_ACTION": {"SELECTED": "{count} item selected | {count} items selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON>"}, "ADD": {"SUGGESTED": {"TITLE": "Example guardrails", "ADD": "Add all", "ADD_SINGLE": "Add this", "SAVE": "Add and save (↵)", "PLACEHOLDER": "Type in another guardrail..."}, "NEW": {"TITLE": "Add a guardrail", "CREATE": "إنشاء", "CANCEL": "إلغاء", "PLACEHOLDER": "Type in another guardrail...", "TEST_ALL": "Test all"}}, "LIST": {"SEARCH_PLACEHOLDER": "Search..."}, "EMPTY_MESSAGE": "No guardrails found. Create or add examples to begin.", "SEARCH_EMPTY_MESSAGE": "No guardrails found for this search.", "API": {"ADD": {"SUCCESS": "Guardrails added successfully", "ERROR": "There was an error adding guardrails, please try again."}, "UPDATE": {"SUCCESS": "Guardrails updated successfully", "ERROR": "There was an error updating guardrails, please try again."}, "DELETE": {"SUCCESS": "Guardrails deleted successfully", "ERROR": "There was an error deleting guardrails, please try again."}}}, "RESPONSE_GUIDELINES": {"TITLE": "Response Guidelines", "DESCRIPTION": "The vibe and structure of your assistant’s replies—clear and friendly? Short and snappy? Detailed and formal?", "BREADCRUMB": {"TITLE": "Response Guidelines"}, "BULK_ACTION": {"SELECTED": "{count} item selected | {count} items selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON>"}, "ADD": {"SUGGESTED": {"TITLE": "Example response guidelines", "ADD": "Add all", "ADD_SINGLE": "Add this", "SAVE": "Add and save (↵)", "PLACEHOLDER": "Type in another response guideline..."}, "NEW": {"TITLE": "Add a response guideline", "CREATE": "إنشاء", "CANCEL": "إلغاء", "PLACEHOLDER": "Type in another response guideline...", "TEST_ALL": "Test all"}}, "LIST": {"SEARCH_PLACEHOLDER": "Search..."}, "EMPTY_MESSAGE": "No response guidelines found. Create or add examples to begin.", "SEARCH_EMPTY_MESSAGE": "No response guidelines found for this search.", "API": {"ADD": {"SUCCESS": "Response Guidelines added successfully", "ERROR": "There was an error adding response guidelines, please try again."}, "UPDATE": {"SUCCESS": "Response Guidelines updated successfully", "ERROR": "There was an error updating response guidelines, please try again."}, "DELETE": {"SUCCESS": "Response Guidelines deleted successfully", "ERROR": "There was an error deleting response guidelines, please try again."}}}, "SCENARIOS": {"TITLE": "Scenarios", "DESCRIPTION": "Give your assistant some context—like “what to do when a user is stuck,” or “how to act during a refund request.”", "BREADCRUMB": {"TITLE": "Scenarios"}, "BULK_ACTION": {"SELECTED": "{count} item selected | {count} items selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON>"}, "ADD": {"SUGGESTED": {"TITLE": "Example scenarios", "ADD": "Add all", "ADD_SINGLE": "Add this", "TOOLS_USED": "Tools used :"}, "NEW": {"CREATE": "Add a scenario", "TITLE": "Create a scenario", "FORM": {"TITLE": {"LABEL": "العنوان", "PLACEHOLDER": "Enter a name for the scenario", "ERROR": "Scenario name is required"}, "DESCRIPTION": {"LABEL": "الوصف", "PLACEHOLDER": "Describe how and where this scenario will be used", "ERROR": "Scenario description is required"}, "INSTRUCTION": {"LABEL": "How to handle", "PLACEHOLDER": "Describe how and where this scenario will be handled", "ERROR": "Scenario content is required"}, "CREATE": "إنشاء", "CANCEL": "إلغاء"}}}, "UPDATE": {"CANCEL": "إلغاء", "UPDATE": "Update changes"}, "LIST": {"SEARCH_PLACEHOLDER": "Search..."}, "EMPTY_MESSAGE": "No scenarios found. Create or add examples to begin.", "SEARCH_EMPTY_MESSAGE": "No scenarios found for this search.", "API": {"ADD": {"SUCCESS": "Scenarios added successfully", "ERROR": "There was an error adding scenarios, please try again."}, "UPDATE": {"SUCCESS": "Scenarios updated successfully", "ERROR": "There was an error updating scenarios, please try again."}, "DELETE": {"SUCCESS": "Scenarios deleted successfully", "ERROR": "There was an error deleting scenarios, please try again."}}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "الرابط", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "نعم، احذف", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON>", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "نعم، احذف", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "الكل"}, "STATUS": {"TITLE": "الحالة", "PENDING": "معلق", "APPROVED": "Approved", "ALL": "الكل"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "قطع الاتصال"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "نعم، احذف", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "صندوق الوارد", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}