{"AUTOMATION": {"HEADER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "Automation can replace and streamline existing processes that require manual effort, such as adding labels and assigning conversations to the most suitable agent. This allows the team to focus on their strengths while reducing time spent on routine tasks.", "LEARN_MORE": "Learn more about automation", "HEADER_BTN_TXT": "Tambah Atura<PERSON>", "LOADING": "Mengambil atura<PERSON> o<PERSON>", "ADD": {"TITLE": "Tambah Atura<PERSON>", "SUBMIT": "Buat", "CANCEL_BUTTON_TEXT": "Batalkan", "FORM": {"NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> nama aturan", "ERROR": "<PERSON><PERSON>"}, "DESC": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> aturan", "ERROR": "Deskripsi di<PERSON>"}, "EVENT": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON>an pilih salah satu", "ERROR": "<PERSON><PERSON><PERSON><PERSON>"}, "CONDITIONS": {"LABEL": "<PERSON><PERSON><PERSON>"}, "ACTIONS": {"LABEL": "<PERSON><PERSON><PERSON>"}}, "CONDITION_BUTTON_LABEL": "Tambah Kondisi", "ACTION_BUTTON_LABEL": "Tambah Aksi", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON> otom<PERSON><PERSON> ber<PERSON> di<PERSON>", "ERROR_MESSAGE": "Tidak dapat membuat aturan <PERSON>, <PERSON><PERSON> coba lagi nanti"}}, "LIST": {"TABLE_HEADER": {"NAME": "<PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON>", "ACTIVE": "Aktif", "CREATED_ON": "Dibuat pada"}, "404": "Tidak ada aturan otomatisasi ditemukan"}, "DELETE": {"TITLE": "<PERSON>pus <PERSON>", "SUBMIT": "Hapus", "CANCEL_BUTTON_TEXT": "Batalkan", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus ", "YES": "Ya, Hapus ", "NO": "Tidak, Simpan "}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON> otom<PERSON><PERSON> ber<PERSON>", "ERROR_MESSAGE": "Tidak dapat mengh<PERSON>us <PERSON>, <PERSON><PERSON> coba lagi nanti"}}, "EDIT": {"TITLE": "<PERSON> <PERSON><PERSON><PERSON>", "SUBMIT": "<PERSON><PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Batalkan", "API": {"SUCCESS_MESSAGE": "Aturan otom<PERSON><PERSON> ber<PERSON>", "ERROR_MESSAGE": "Tidak dapat memperbarui aturan <PERSON>, <PERSON><PERSON> coba lagi nanti"}}, "CLONE": {"TOOLTIP": "Klon", "API": {"SUCCESS_MESSAGE": "Otomati<PERSON><PERSON> berhasil dikloning", "ERROR_MESSAGE": "Tidak dapat mengkloning at<PERSON><PERSON>, <PERSON><PERSON> coba lagi nanti"}}, "FORM": {"EDIT": "Edit", "CREATE": "Buat", "DELETE": "Hapus", "CANCEL": "Batalkan", "RESET_MESSAGE": "Mengubah jenis peristiwa akan mengatur ulang kondisi dan peristiwa yang Anda tambahkan di bawah"}, "CONDITION": {"DELETE_MESSAGE": "Anda harus memiliki setidaknya satu kondisi untuk disimpan", "CONTACT_CUSTOM_ATTR_LABEL": "Atribut Kustom Kontak", "CONVERSATION_CUSTOM_ATTR_LABEL": "Atribut Kustom Percakapan"}, "ACTION": {"DELETE_MESSAGE": "Anda harus memiliki setidaknya satu aksi untuk disimpan", "TEAM_MESSAGE_INPUT_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> pesan <PERSON>a di sini", "TEAM_DROPDOWN_PLACEHOLDER": "<PERSON><PERSON><PERSON> tim", "EMAIL_INPUT_PLACEHOLDER": "Enter email", "URL_INPUT_PLACEHOLDER": "Enter URL"}, "TOGGLE": {"ACTIVATION_TITLE": "Aktifkan Aturan O<PERSON>", "DEACTIVATION_TITLE": "Nonaktifkan At<PERSON>", "ACTIVATION_DESCRIPTION": "<PERSON>dakan ini akan mengaktifkan aturan otomatisasi '{automationName}'. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "DEACTIVATION_DESCRIPTION": "<PERSON>dakan ini akan menonaktifkan aturan otomatisasi '{automationName}'. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "ACTIVATION_SUCCESFUL": "<PERSON><PERSON><PERSON>", "DEACTIVATION_SUCCESFUL": "<PERSON><PERSON><PERSON>", "ACTIVATION_ERROR": "Tidak dapat Mengakt<PERSON>, <PERSON><PERSON> coba lagi nanti", "DEACTIVATION_ERROR": "Tidak dapat Menonakt<PERSON>, <PERSON><PERSON> coba lagi nanti", "CONFIRMATION_LABEL": "Ya", "CANCEL_LABEL": "Tidak"}, "ATTACHMENT": {"UPLOAD_ERROR": "Tidak dapat <PERSON>, <PERSON><PERSON> coba lagi", "LABEL_IDLE": "<PERSON><PERSON><PERSON>", "LABEL_UPLOADING": "Mengunggah...", "LABEL_UPLOADED": "<PERSON><PERSON><PERSON><PERSON>", "LABEL_UPLOAD_FAILED": "<PERSON><PERSON>"}, "ERRORS": {"ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_REQUIRED": "<PERSON><PERSON>", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998", "ACTION_PARAMETERS_REQUIRED": "Action parameters are required", "ATLEAST_ONE_CONDITION_REQUIRED": "At least one condition is required", "ATLEAST_ONE_ACTION_REQUIRED": "At least one action is required"}, "NONE_OPTION": "Tidak ada", "EVENTS": {"CONVERSATION_CREATED": "Percakapan Dibuat", "CONVERSATION_UPDATED": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE_CREATED": "Message Created", "CONVERSATION_RESOLVED": "Conversation Resolved", "CONVERSATION_OPENED": "Conversation Opened"}, "ACTIONS": {"ASSIGN_AGENT": "Assign to Agent", "ASSIGN_TEAM": "Assign a Team", "ADD_LABEL": "Add a Label", "REMOVE_LABEL": "Remove a Label", "SEND_EMAIL_TO_TEAM": "Send an Email to Team", "SEND_EMAIL_TRANSCRIPT": "Send an Email Transcript", "MUTE_CONVERSATION": "<PERSON><PERSON><PERSON>", "SNOOZE_CONVERSATION": "<PERSON><PERSON>", "RESOLVE_CONVERSATION": "Selesai<PERSON>", "SEND_WEBHOOK_EVENT": "Send Webhook Event", "SEND_ATTACHMENT": "Send Attachment", "SEND_MESSAGE": "Send a Message", "ADD_PRIVATE_NOTE": "Add a Private Note", "CHANGE_PRIORITY": "Ubah Prioritas", "ADD_SLA": "Add SLA", "OPEN_CONVERSATION": "<PERSON><PERSON>"}, "MESSAGE_TYPES": {"INCOMING": "Incoming Message", "OUTGOING": "Outgoing Message"}, "PRIORITY_TYPES": {"NONE": "Tidak ada", "LOW": "Rendah", "MEDIUM": "Sedang", "HIGH": "Tingg<PERSON>", "URGENT": "Penting"}, "ATTRIBUTES": {"MESSAGE_TYPE": "Message Type", "MESSAGE_CONTAINS": "Message Contains", "EMAIL": "Email", "INBOX": "Kotak masuk", "CONVERSATION_LANGUAGE": "Conversation Language", "PHONE_NUMBER": "Nomor Telepon", "STATUS": "Status", "BROWSER_LANGUAGE": "<PERSON><PERSON> Browser", "MAIL_SUBJECT": "Email Subject", "COUNTRY_NAME": "Negara", "REFERER_LINK": "Referrer Link", "ASSIGNEE_NAME": "Assignee", "TEAM_NAME": "<PERSON>", "PRIORITY": "Prioritas"}}}