{"AUTOMATION": {"HEADER": "Automatisations", "DESCRIPTION": "Automation can replace and streamline existing processes that require manual effort, such as adding labels and assigning conversations to the most suitable agent. This allows the team to focus on their strengths while reducing time spent on routine tasks.", "LEARN_MORE": "Learn more about automation", "HEADER_BTN_TXT": "Ajouter une règle d'automatisation", "LOADING": "Récupération des règles d'automatisation", "ADD": {"TITLE": "Ajouter une règle d'automatisation", "SUBMIT": "<PERSON><PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Annuler", "FORM": {"NAME": {"LABEL": "Nom de la règle", "PLACEHOLDER": "Entrez le nom de la règle", "ERROR": "Le nom est requis"}, "DESC": {"LABEL": "Description", "PLACEHOLDER": "Entrez la description de la règle", "ERROR": "La description est requise"}, "EVENT": {"LABEL": "Événement", "PLACEHOLDER": "Veuillez en sélectionner un", "ERROR": "L'événement est requis"}, "CONDITIONS": {"LABEL": "Condition"}, "ACTIONS": {"LABEL": "Actions"}}, "CONDITION_BUTTON_LABEL": "Ajouter une condition", "ACTION_BUTTON_LABEL": "Ajouter une action", "API": {"SUCCESS_MESSAGE": "Règle d'automatisation ajoutée avec succès", "ERROR_MESSAGE": "Impossible de créer une règle d'automatisation, veuil<PERSON><PERSON> réessayer plus tard"}}, "LIST": {"TABLE_HEADER": {"NAME": "Nom", "DESCRIPTION": "Description", "ACTIVE": "Actif", "CREATED_ON": "<PERSON><PERSON><PERSON>"}, "404": "Aucune règle d'automatisation trouvée"}, "DELETE": {"TITLE": "Supprimer la règle d'automatisation", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Annuler", "CONFIRM": {"TITLE": "Confirmer la <PERSON>", "MESSAGE": "Êtes-vous sûr de vouloir supprimer ", "YES": "<PERSON><PERSON>, supprimer ", "NO": "Non, Conserver "}, "API": {"SUCCESS_MESSAGE": "Intégration supprimée avec succès", "ERROR_MESSAGE": "Impossible de créer une règle d'automatisation, veuil<PERSON><PERSON> réessayer plus tard"}}, "EDIT": {"TITLE": "Modifier la règle d'automatisation", "SUBMIT": "Mettre à jour", "CANCEL_BUTTON_TEXT": "Annuler", "API": {"SUCCESS_MESSAGE": "Règle d'automatisation mise à jour avec succès", "ERROR_MESSAGE": "Impossible de mettre à jour la règle d'automatisation, ve<PERSON><PERSON><PERSON> réessayer plus tard"}}, "CLONE": {"TOOLTIP": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Automation clonée avec succès", "ERROR_MESSAGE": "Impossible de cloner la règle d'automatisation, ve<PERSON><PERSON><PERSON> réessayer plus tard"}}, "FORM": {"EDIT": "Modifier", "CREATE": "<PERSON><PERSON><PERSON>", "DELETE": "<PERSON><PERSON><PERSON><PERSON>", "CANCEL": "Annuler", "RESET_MESSAGE": "Changer le type d'événement réinitialisera les conditions et les événements que vous avez ajoutés ci-dessous"}, "CONDITION": {"DELETE_MESSAGE": "Vous devez avoir au moins une condition pour enregistrer", "CONTACT_CUSTOM_ATTR_LABEL": "Attributs personnalisés des contacts", "CONVERSATION_CUSTOM_ATTR_LABEL": "Attributs personnalisés de la conversation"}, "ACTION": {"DELETE_MESSAGE": "Vous devez avoir au moins une action pour enregistrer", "TEAM_MESSAGE_INPUT_PLACEHOLDER": "Saisissez votre message ici", "TEAM_DROPDOWN_PLACEHOLDER": "Sélectionner une équipe", "EMAIL_INPUT_PLACEHOLDER": "Enter email", "URL_INPUT_PLACEHOLDER": "Enter URL"}, "TOGGLE": {"ACTIVATION_TITLE": "Activer la règle d'automatisation", "DEACTIVATION_TITLE": "Activer la règle d'automatisation", "ACTIVATION_DESCRIPTION": "Cette action activera la règle d'automatisation '{automationName}'. Êtes-vous sur de vouloir continuer?", "DEACTIVATION_DESCRIPTION": "Cette action activera la règle d'automatisation '{automationName}'. Êtes-vous sur de vouloir continuer?", "ACTIVATION_SUCCESFUL": "Règle d'automatisation activée avec succès", "DEACTIVATION_SUCCESFUL": "Règle d'automatisation désactivée avec succès", "ACTIVATION_ERROR": "Impossible d'activer l'automatisation, ve<PERSON><PERSON><PERSON> réessayer plus tard", "DEACTIVATION_ERROR": "Impossible de désactiver l'automatisation, ve<PERSON><PERSON><PERSON> réessayer plus tard", "CONFIRMATION_LABEL": "O<PERSON>", "CANCEL_LABEL": "Non"}, "ATTACHMENT": {"UPLOAD_ERROR": "Impossible d'envoyer la pièce jointe, ve<PERSON><PERSON><PERSON> réessayer", "LABEL_IDLE": "Charger une pièce jointe", "LABEL_UPLOADING": "Téléversement...", "LABEL_UPLOADED": "Téléchargé avec succès", "LABEL_UPLOAD_FAILED": "Échec de l'envoi"}, "ERRORS": {"ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_REQUIRED": "La valeur est requise", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998", "ACTION_PARAMETERS_REQUIRED": "Action parameters are required", "ATLEAST_ONE_CONDITION_REQUIRED": "At least one condition is required", "ATLEAST_ONE_ACTION_REQUIRED": "At least one action is required"}, "NONE_OPTION": "Aucun", "EVENTS": {"CONVERSATION_CREATED": "Conversation créée", "CONVERSATION_UPDATED": "Conversation mise à jour", "MESSAGE_CREATED": "Message créé", "CONVERSATION_RESOLVED": "Conversation Resolved", "CONVERSATION_OPENED": "Conversation ouverte"}, "ACTIONS": {"ASSIGN_AGENT": "Assigner à un agent", "ASSIGN_TEAM": "Assigner une équipe", "ADD_LABEL": "Ajouter une étiquette", "REMOVE_LABEL": "Supprimer une étiquette", "SEND_EMAIL_TO_TEAM": "Envoyer un e-mail à l'équipe", "SEND_EMAIL_TRANSCRIPT": "Envoyer une transcription par e-mail", "MUTE_CONVERSATION": "Mettre la conversation en sourdine", "SNOOZE_CONVERSATION": "<PERSON><PERSON><PERSON><PERSON><PERSON> la <PERSON>", "RESOLVE_CONVERSATION": "Résoudre la conversation", "SEND_WEBHOOK_EVENT": "Envoyer un événement Webhook", "SEND_ATTACHMENT": "Envoyer la pièce jointe", "SEND_MESSAGE": "Envoyer un message", "ADD_PRIVATE_NOTE": "Ajouter une note privée", "CHANGE_PRIORITY": "Modifier la priorité", "ADD_SLA": "Add SLA", "OPEN_CONVERSATION": "<PERSON><PERSON><PERSON><PERSON><PERSON> la <PERSON>"}, "MESSAGE_TYPES": {"INCOMING": "Incoming Message", "OUTGOING": "Outgoing Message"}, "PRIORITY_TYPES": {"NONE": "Aucun", "LOW": "Faible", "MEDIUM": "<PERSON><PERSON><PERSON>", "HIGH": "<PERSON><PERSON><PERSON>", "URGENT": "<PERSON><PERSON>"}, "ATTRIBUTES": {"MESSAGE_TYPE": "Type de message", "MESSAGE_CONTAINS": "Le message contient", "EMAIL": "<PERSON><PERSON><PERSON>", "INBOX": "<PERSON><PERSON><PERSON>", "CONVERSATION_LANGUAGE": "<PERSON>ue de la conversation", "PHONE_NUMBER": "Numéro de téléphone", "STATUS": "État", "BROWSER_LANGUAGE": "<PERSON><PERSON>", "MAIL_SUBJECT": "Objet de l'email", "COUNTRY_NAME": "Pays", "REFERER_LINK": "<PERSON><PERSON> de réfé<PERSON>ce", "ASSIGNEE_NAME": "Assignee", "TEAM_NAME": "Équipes", "PRIORITY": "Priorité"}}}