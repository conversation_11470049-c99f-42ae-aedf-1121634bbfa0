{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "<PERSON><PERSON><PERSON>", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integraciones", "DESCRIPTION": "Chatwoot se integra con múltiples herramientas y servicios para mejorar la eficiencia de tu equipo. Explora la lista de abajo para configurar tus aplicaciones favoritas.", "LEARN_MORE": "Más información acerca de integraciones", "LOADING": "Obteniendo integraciones", "CAPTAIN": {"DISABLED": "El capitán no está habilitado en tu cuenta.", "CLICK_HERE_TO_CONFIGURE": "Haz clic aquí para configurar", "LOADING_CONSOLE": "Cargando consola del capitán...", "FAILED_TO_LOAD_CONSOLE": "No se pudo cargar la consola del capitán. Por favor, actualiza e inténtalo de nuevo."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Eventos suscritos", "LEARN_MORE": "Aprenda más sobre webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "DESC": "Los eventos Webhook te proporcionan la información en tiempo real sobre lo que está sucediendo en tu cuenta de Chatwoot. Por favor, introduce una URL válida para configurar un callback.", "SUBSCRIPTIONS": {"LABEL": "Eventos", "EVENTS": {"CONVERSATION_CREATED": "Conversación creada", "CONVERSATION_STATUS_CHANGED": "Estado de la conversación cambiado", "CONVERSATION_UPDATED": "Conversación actualizada", "MESSAGE_CREATED": "<PERSON><PERSON><PERSON> c<PERSON>o", "MESSAGE_UPDATED": "<PERSON><PERSON>je <PERSON>", "WEBWIDGET_TRIGGERED": "Widget de Live Chat abierto por el usuario", "CONTACT_CREATED": "<PERSON><PERSON> c<PERSON>o", "CONTACT_UPDATED": "<PERSON>o <PERSON>", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "URL de Webhook", "PLACEHOLDER": "Ejemplo: {webhookExampleURL}", "ERROR": "Por favor, introduzca una URL válida"}, "EDIT_SUBMIT": "<PERSON>ual<PERSON><PERSON> webhook", "ADD_SUBMIT": "<PERSON><PERSON><PERSON> webhook"}, "TITLE": "Webhook", "CONFIGURE": "Configurar", "HEADER": "Configuración de Webhook", "HEADER_BTN_TXT": "<PERSON><PERSON>dir nuevo webhook", "LOADING": "Obteniendo webhooks adjuntos", "SEARCH_404": "No hay elementos que coincidan con esta consulta", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhooks son callbacks HTTP que se pueden definir para cada cuenta. Son activados por eventos como la creación de mensajes en Chatwoot. Puede crear más de un webhook para esta cuenta. <br /><br /> Para crear un webhook <b></b>, haga clic en el <b>Añadir un nuevo webhook</b> botón. También puede eliminar cualquier webhook existente haciendo clic en el botón Borrar.</p>", "LIST": {"404": "No hay webhooks configurados para esta cuenta.", "TITLE": "Administrar webhooks", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Final de Webhook", "ACTIONS": "Acciones"}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON>", "TITLE": "<PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Configuración de webhook actualizada correctamente", "ERROR_MESSAGE": "No se pudo conectar al servidor Woot, por favor inténtalo de nuevo más tarde"}}, "ADD": {"CANCEL": "<PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON>dir nuevo webhook", "API": {"SUCCESS_MESSAGE": "Configuración de webhook añadida correctamente", "ERROR_MESSAGE": "No se pudo conectar al servidor Woot, por favor inténtalo de nuevo más tarde"}}, "DELETE": {"BUTTON_TEXT": "Eliminar", "API": {"SUCCESS_MESSAGE": "Webhook eliminado correctamente", "ERROR_MESSAGE": "No se pudo conectar al servidor Woot, por favor inténtalo de nuevo más tarde"}, "CONFIRM": {"TITLE": "Confirmar eliminación", "MESSAGE": "¿Está seguro de eliminar el webhook? ({webhookURL})", "YES": "Sí, eliminar ", "NO": "No, manten<PERSON>lo"}}}, "SLACK": {"DELETE": "Eliminar", "DELETE_CONFIRMATION": {"TITLE": "Eliminar la integración", "MESSAGE": "¿Está seguro que desea eliminar la integración? Si lo hace, perderá el acceso a las conversaciones en su espacio de trabajo Slack."}, "HELP_TEXT": {"TITLE": "¿Cómo utilizar la Integración Slack?", "BODY": "Con esta integración, todas tus conversaciones entrantes serán sincronizadas con el canal ***{selectedChannelName}*** en tu espacio de trabajo en Slack. Puedes administrar todas tus conversaciones con los clientes directamente en tu canal y nunca perder un mensaje.\n\nEstas son las principales características de la integración:\n\n**Responda a conversaciones desde Slack:** Para responder a una conversación en el canal Slack ***{selectedChannelName}***, simplemente escriba el mensaje y envíalo como un hilo. Esto creará una respuesta que se enviará al cliente mediante Chatwoot. ¡Es así de simple!\n\n**Crea notas privadas:** Si quieres crear una nota privada en lugar de una respuesta, comience su mensaje con ***`note:`***. Esto asegurará que tu mensaje se mantenga privado y no será visible al cliente.\n\n**Asociar un perfil de agente:** Si la persona que responde en Slack tiene un agente de perfil en Chatwoot bajo el mismo correo electrónico, las respuestas serán asociadas con ese perfil de agente automáticamente. Esto quiere decir que puedes fácilmente estar al tanto de quién dijo qué y cuándo. Por el otro lado, cuando la persona que responde no tiene un perfil de agente, las respuestas aparecerán desde el perfil de bot al cliente.", "SELECTED": "sele<PERSON><PERSON><PERSON>"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Seleccione un canal", "UPDATE": "Actualizar", "BUTTON_TEXT": "Conectar canal", "DESCRIPTION": "Su espacio de trabajo de Slack ahora esta enlazado con Chatwoot. Sin embarbo, la integración esta actualmente inactiva. Para activar la integración y conectar un canal a Chatwoot, por favor haga clic en el siguiente boton.\n\n**Nota.** Si esta intentando conectar un canal privado, agregue la aplicación Chatwoot al canal de Slack antes de proceder con este paso.", "ATTENTION_REQUIRED": "Atención requerida", "EXPIRED": "Su integración de Slack ha caducado. Para continuar recibiendo mensajes en Slack, por favor elimine la integración y vuelva a conectar su área de trabajo."}, "UPDATE_ERROR": "Se presento un error actualizando la integración, por favor inténtelo nuevamente", "UPDATE_SUCCESS": "El canal está conectado correctamente", "FAILED_TO_FETCH_CHANNELS": "Hubo un error obteniendo los canales de Slack, por favor inténtalo de nuevo"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Haga clic aquí para unirse", "LEAVE_THE_ROOM": "Abandonar la sala", "START_VIDEO_CALL_HELP_TEXT": "Iniciar una nueva videollamada con el cliente", "JOIN_ERROR": "Hubo un error al unirse a la llamada, por favor intente de nuevo", "CREATE_ERROR": "Hubo un error al crear un enlace de reunión, por favor inténtelo de nuevo"}, "OPEN_AI": {"AI_ASSIST": "Asistencia AI", "WITH_AI": " {option} con IA ", "OPTIONS": {"REPLY_SUGGESTION": "Responder sugerencia", "SUMMARIZE": "Resumir", "REPHRASE": "Mejorar escritura", "FIX_SPELLING_GRAMMAR": "Corregir ortografía y gramática", "SHORTEN": "Acortar", "EXPAND": "Expandir", "MAKE_FRIENDLY": "Cambiar tono de mensaje a amigable", "MAKE_FORMAL": "Usar tono formal", "SIMPLIFY": "Simplificar"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Contenido de borrador", "GENERATED_TITLE": "Contenido generado", "AI_WRITING": "AI está escribiendo", "BUTTONS": {"APPLY": "Usar esta sugerencia", "CANCEL": "<PERSON><PERSON><PERSON>"}}, "CTA_MODAL": {"TITLE": "Integrar con OpenAI", "DESC": "Trae las características avanzadas de IA a tu panel de control con los modelos GPT de OpenAI. Para empezar, introduce la clave API desde tu cuenta OpenAI.", "KEY_PLACEHOLDER": "Introduzca su clave API OpenAI", "BUTTONS": {"NEED_HELP": "¿Necesitas ayuda?", "DISMISS": "Descar<PERSON>", "FINISH": "Finalizar configuración"}, "DISMISS_MESSAGE": "<PERSON><PERSON><PERSON> configurar la integración OpenAI más tarde cuando quieras.", "SUCCESS_MESSAGE": "Configuración de integración OpenAI exitosa"}, "TITLE": "Mejorar con IA", "SUMMARY_TITLE": "Resumen con IA", "REPLY_TITLE": "Responder sugerencia con IA", "SUBTITLE": "Se generará una respuesta mejorada usando IA basada en tu borrador actual.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Profesional", "FRIENDLY": "Amigable"}}, "BUTTONS": {"GENERATE": "Generar", "GENERATING": "Generando...", "CANCEL": "<PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "Hubo un error al procesar el contenido, por favor verifique su clave API OpenAI e inténtelo de nuevo"}, "DELETE": {"BUTTON_TEXT": "Eliminar", "API": {"SUCCESS_MESSAGE": "Integración eliminada correctamente"}}, "CONNECT": {"BUTTON_TEXT": "Conectar"}, "DASHBOARD_APPS": {"TITLE": "Panel de aplicaciones", "HEADER_BTN_TXT": "Añadir una nueva aplicación", "SIDEBAR_TXT": "<p><b>Aplicaciones de panel</b></p><p>Aplicaciones de panel de control permiten a las organizaciones incrustar una aplicación dentro del panel de control de Chatwoot para proporcionar el contexto para los agentes de atención al cliente. Esta característica le permite crear una aplicación de forma independiente e incrustarla dentro del panel de control para proporcionar información de usuario, sus pedidos, o su historial de pagos anterior.</p><p>Cuando incrustas tu aplicación usando el panel de control en Chatwoot, tu aplicación obtendrá el contexto de la conversación y el contacto como un evento de ventana. Implementa un oyente para el evento del mensaje en tu página para recibir el contexto.</p><p>Para añadir una nueva aplicación de panel, haga clic en el botón 'Añadir una nueva aplicación de panel'.</p>", "DESCRIPTION": "Las aplicaciones de panel permiten a las organizaciones incrustar una aplicación dentro del panel de control para proporcionar el contexto para los agentes de soporte al cliente. Esta función le permite crear una aplicación de forma independiente e incrustada para proporcionar información de usuario, sus pedidos o su historial de pagos anterior.", "LEARN_MORE": "Aprende más sobre el panel de aplicaciones", "LIST": {"404": "Todavía no hay aplicaciones configuradas en esta cuenta", "LOADING": "Obteniendo aplicaciones del tablero...", "TABLE_HEADER": {"NAME": "Nombre", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Editar aplicación", "DELETE_TOOLTIP": "Eliminar aplicación"}, "FORM": {"TITLE_LABEL": "Nombre", "TITLE_PLACEHOLDER": "Ingrese un nombre para su aplicación de tablero", "TITLE_ERROR": "Se requiere un nombre para el panel de control", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Introduzca la URL del endpoint donde se aloja su aplicación", "URL_ERROR": "Se requiere una URL válida"}, "CREATE": {"HEADER": "Añadir una nueva aplicación", "FORM_SUBMIT": "Enviar", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "Panel de control configurado correctamente", "API_ERROR": "No pudimos crear una aplicación. Por favor, inténtalo de nuevo más tarde"}, "UPDATE": {"HEADER": "Editar panel de aplicación", "FORM_SUBMIT": "Actualizar", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "Panel de control actualizado correctamente", "API_ERROR": "No pudimos actualizar la aplicación. Por favor, inténtalo de nuevo más tarde"}, "DELETE": {"CONFIRM_YES": "Sí, eliminarlo", "CONFIRM_NO": "No, manten<PERSON>lo", "TITLE": "Confirme eliminación", "MESSAGE": "¿Está seguro que desea eliminar la aplicación - {appName}?", "API_SUCCESS": "Panel de control eliminado con éxito", "API_ERROR": "No pudimos eliminar la aplicación. Por favor, inténtalo de nuevo más tarde"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Crear/Enlazar <PERSON>ar", "LOADING": "Cargando problemas lineales...", "LOADING_ERROR": "Hubo un error al recuperar los problemas lineales, por favor inténtalo de nuevo", "CREATE": "<PERSON><PERSON><PERSON>", "LINK": {"SEARCH": "Buscar problemas", "SELECT": "Se<PERSON><PERSON>onar problema", "TITLE": "Enlace", "EMPTY_LIST": "No se encontraron problemas lineales", "LOADING": "Cargando", "ERROR": "Hubo un error al recuperar los problemas lineales, por favor inténtalo de nuevo", "LINK_SUCCESS": "Incidencia enlazada correctamente", "LINK_ERROR": "Ocurrió un error al enlazar la incidencia. Inténtelo de nuevo", "LINK_TITLE": "Conversación (#{conversationId}) con {name}"}, "ADD_OR_LINK": {"TITLE": "<PERSON><PERSON>r/enlazar incidencia con Linear", "DESCRIPTION": "Crear incidencias en Linear desde conversaciones, o enlazar existentes para un seguimiento fluido.", "FORM": {"TITLE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "REQUIRED_ERROR": "El título es obligatorio"}, "DESCRIPTION": {"LABEL": "Descripción", "PLACEHOLDER": "Introducir descripción"}, "TEAM": {"LABEL": "Equipo", "PLACEHOLDER": "Seleccionar equipo", "SEARCH": "Buscar equipos", "REQUIRED_ERROR": "El equipo es requerido"}, "ASSIGNEE": {"LABEL": "Cesionario", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SEARCH": "Buscar asignado"}, "PRIORITY": {"LABEL": "Prioridad", "PLACEHOLDER": "Seleccionar prioridad", "SEARCH": "Buscar prioridad"}, "LABEL": {"LABEL": "Etiqueta", "PLACEHOLDER": "Seleecionar etiqueta", "SEARCH": "Buscar etiqueta"}, "STATUS": {"LABEL": "Estado", "PLACEHOLDER": "Seleccionar estado", "SEARCH": "Buscar estado"}, "PROJECT": {"LABEL": "Proyecto", "PLACEHOLDER": "Seleccionar proyecto", "SEARCH": "Buscar proyecto"}}, "CREATE": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE_SUCCESS": "Incidencia creada correctamente", "CREATE_ERROR": "Hubo un error creando la incidencia. Inténtalo de nuevo", "LOADING_TEAM_ERROR": "Hubo un error al coger los equipos. Inténtalo de nuevo", "LOADING_TEAM_ENTITIES_ERROR": "Hubo un error al coger las entidades del equipo. Inténtalo de nuevo"}, "ISSUE": {"STATUS": "Estado", "PRIORITY": "Prioridad", "ASSIGNEE": "Cesionario", "LABELS": "Etiquetas", "CREATED_AT": "<PERSON><PERSON><PERSON> en {createdAt}"}, "UNLINK": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "SUCCESS": "Problema desvinculado con éxito", "ERROR": "Se ha producido un error al desvincular el problema, inténtelo de nuevo"}, "NO_LINKED_ISSUES": "No linked issues found", "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Sí, eliminar", "CANCEL": "<PERSON><PERSON><PERSON>"}, "CTA": {"TITLE": "Connect to Linear", "AGENT_DESCRIPTION": "Linear workspace is not connected. Request your administrator to connect a workspace to use this integration.", "DESCRIPTION": "Linear workspace is not connected. Click the button below to connect your workspace to use this integration.", "BUTTON_TEXT": "Connect Linear workspace"}}, "NOTION": {"DELETE": {"TITLE": "Are you sure you want to delete the Notion integration?", "MESSAGE": "Deleting this integration will remove access to your Notion workspace and stop all related functionality.", "CONFIRM": "Sí, eliminar", "CANCEL": "<PERSON><PERSON><PERSON>"}}}, "CAPTAIN": {"NAME": "Capitán", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"TITLE": "Copilot", "TRY_THESE_PROMPTS": "Prueba estas sugerencias", "PANEL_TITLE": "Get started with Copilot", "KICK_OFF_MESSAGE": "Need a quick summary, want to check past conversations, or draft a better reply? <PERSON><PERSON><PERSON>’s here to speed things up.", "SEND_MESSAGE": "Enviar mensaje...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "Tú", "USE": "Use this", "RESET": "Reset", "SHOW_STEPS": "Show steps", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}, "HIGH_PRIORITY": {"LABEL": "High priority conversations", "CONTENT": "Give me a summary of all high priority open conversations. Include the conversation ID, customer name (if available), last message content, and assigned agent. Group by status if relevant."}, "LIST_CONTACTS": {"LABEL": "List contacts", "CONTENT": "Show me the list of top 10 contacts. Include name, email or phone number (if available), last seen time, tags (if any)."}}}, "PLAYGROUND": {"USER": "Tú", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "Escribe tu mensaje...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "<PERSON><PERSON><PERSON><PERSON>ora", "CANCEL_ANYTIME": "<PERSON>uede cambiar o cancelar su plan en cualquier momento"}, "ENTERPRISE_PAYWALL": {"UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Por favor, comuníquese con su administrador para la actualización."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "<PERSON><PERSON><PERSON>", "EDIT": "Actualizar"}, "ASSISTANTS": {"HEADER": "Assistants", "NO_ASSISTANTS_AVAILABLE": "There are no assistants available in your account.", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Sí, eliminar", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "Actualizar", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Características", "TOOLS": "Tools "}, "NAME": {"LABEL": "Nombre", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "TEMPERATURE": {"LABEL": "Response Temperature", "DESCRIPTION": "Adjust how creative or restrictive the assistant's responses should be. Lower values produce more focused and deterministic responses, while higher values allow for more creative and varied outputs."}, "DESCRIPTION": {"LABEL": "Descripción", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Características", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions.", "ALLOW_CITATIONS": "Include source citations in responses"}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "SETTINGS": {"BREADCRUMB": {"ASSISTANT": "Assistant"}, "BASIC_SETTINGS": {"TITLE": "Basic settings", "DESCRIPTION": "Customize what the assistant says when ending a conversation or transferring to a human."}, "SYSTEM_SETTINGS": {"TITLE": "System settings", "DESCRIPTION": "Customize what the assistant says when ending a conversation or transferring to a human."}, "CONTROL_ITEMS": {"TITLE": "The Fun Stuff", "DESCRIPTION": "Add more control to the assistant. (a bit more visual like a story : Query guardrail → scenarios → output) Nudges user to actually utilise these.", "OPTIONS": {"GUARDRAILS": {"TITLE": "Guardrails", "DESCRIPTION": "Keeps things on track—only the kinds of questions you want your assistant to answer, nothing off-limits or off-topic."}, "SCENARIOS": {"TITLE": "Scenarios", "DESCRIPTION": "Give your assistant some context—like “what to do when a user is stuck,” or “how to act during a refund request.”"}, "RESPONSE_GUIDELINES": {"TITLE": "Response guidelines", "DESCRIPTION": "The vibe and structure of your assistant’s replies—clear and friendly? Short and snappy? Detailed and formal?"}}}}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}, "GUARDRAILS": {"TITLE": "Guardrails", "DESCRIPTION": "Keeps things on track—only the kinds of questions you want your assistant to answer, nothing off-limits or off-topic.", "BREADCRUMB": {"TITLE": "Guardrails"}, "BULK_ACTION": {"SELECTED": "{count} item selected | {count} items selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_DELETE_BUTTON": "Eliminar"}, "ADD": {"SUGGESTED": {"TITLE": "Example guardrails", "ADD": "Add all", "ADD_SINGLE": "Add this", "SAVE": "Add and save (↵)", "PLACEHOLDER": "Type in another guardrail..."}, "NEW": {"TITLE": "Add a guardrail", "CREATE": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Type in another guardrail...", "TEST_ALL": "Test all"}}, "LIST": {"SEARCH_PLACEHOLDER": "Buscar..."}, "EMPTY_MESSAGE": "No guardrails found. Create or add examples to begin.", "SEARCH_EMPTY_MESSAGE": "No guardrails found for this search.", "API": {"ADD": {"SUCCESS": "Guardrails added successfully", "ERROR": "There was an error adding guardrails, please try again."}, "UPDATE": {"SUCCESS": "Guardrails updated successfully", "ERROR": "There was an error updating guardrails, please try again."}, "DELETE": {"SUCCESS": "Guardrails deleted successfully", "ERROR": "There was an error deleting guardrails, please try again."}}}, "RESPONSE_GUIDELINES": {"TITLE": "Response Guidelines", "DESCRIPTION": "The vibe and structure of your assistant’s replies—clear and friendly? Short and snappy? Detailed and formal?", "BREADCRUMB": {"TITLE": "Response Guidelines"}, "BULK_ACTION": {"SELECTED": "{count} item selected | {count} items selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_DELETE_BUTTON": "Eliminar"}, "ADD": {"SUGGESTED": {"TITLE": "Example response guidelines", "ADD": "Add all", "ADD_SINGLE": "Add this", "SAVE": "Add and save (↵)", "PLACEHOLDER": "Type in another response guideline..."}, "NEW": {"TITLE": "Add a response guideline", "CREATE": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Type in another response guideline...", "TEST_ALL": "Test all"}}, "LIST": {"SEARCH_PLACEHOLDER": "Buscar..."}, "EMPTY_MESSAGE": "No response guidelines found. Create or add examples to begin.", "SEARCH_EMPTY_MESSAGE": "No response guidelines found for this search.", "API": {"ADD": {"SUCCESS": "Response Guidelines added successfully", "ERROR": "There was an error adding response guidelines, please try again."}, "UPDATE": {"SUCCESS": "Response Guidelines updated successfully", "ERROR": "There was an error updating response guidelines, please try again."}, "DELETE": {"SUCCESS": "Response Guidelines deleted successfully", "ERROR": "There was an error deleting response guidelines, please try again."}}}, "SCENARIOS": {"TITLE": "Scenarios", "DESCRIPTION": "Give your assistant some context—like “what to do when a user is stuck,” or “how to act during a refund request.”", "BREADCRUMB": {"TITLE": "Scenarios"}, "BULK_ACTION": {"SELECTED": "{count} item selected | {count} items selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_DELETE_BUTTON": "Eliminar"}, "ADD": {"SUGGESTED": {"TITLE": "Example scenarios", "ADD": "Add all", "ADD_SINGLE": "Add this", "TOOLS_USED": "Tools used :"}, "NEW": {"CREATE": "Add a scenario", "TITLE": "Create a scenario", "FORM": {"TITLE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter a name for the scenario", "ERROR": "Scenario name is required"}, "DESCRIPTION": {"LABEL": "Descripción", "PLACEHOLDER": "Describe how and where this scenario will be used", "ERROR": "Scenario description is required"}, "INSTRUCTION": {"LABEL": "How to handle", "PLACEHOLDER": "Describe how and where this scenario will be handled", "ERROR": "Scenario content is required"}, "CREATE": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON>"}}}, "UPDATE": {"CANCEL": "<PERSON><PERSON><PERSON>", "UPDATE": "Update changes"}, "LIST": {"SEARCH_PLACEHOLDER": "Buscar..."}, "EMPTY_MESSAGE": "No scenarios found. Create or add examples to begin.", "SEARCH_EMPTY_MESSAGE": "No scenarios found for this search.", "API": {"ADD": {"SUCCESS": "Scenarios added successfully", "ERROR": "There was an error adding scenarios, please try again."}, "UPDATE": {"SUCCESS": "Scenarios updated successfully", "ERROR": "There was an error updating scenarios, please try again."}, "DELETE": {"SUCCESS": "Scenarios deleted successfully", "ERROR": "There was an error deleting scenarios, please try again."}}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Sí, eliminar", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "Preguntas frecuentes", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "SELECT_ALL": "Select all ({count})", "UNSELECT_ALL": "Unselect all ({count})", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Eliminar", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Sí, eliminar", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Todos"}, "STATUS": {"TITLE": "Estado", "PENDING": "Pendientes", "APPROVED": "Approved", "ALL": "Todos"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Desconectar"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Sí, eliminar", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Bandeja de entrada", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}