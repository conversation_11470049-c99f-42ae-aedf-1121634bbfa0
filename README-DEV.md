# 🚀 Chatwoot 开发环境快速指南

本项目已配置完整的容器化开发环境，支持代码热重载、实时调试和完整的开发工具链。

## ⚡ 快速开始

### 1. 一键启动
```bash
# 克隆项目后，直接运行
./start-dev.sh
```

### 2. 访问应用
启动完成后访问：
- **🌐 主应用**: http://localhost:3000
- **⚡ Vite 开发服务器**: http://localhost:3036  
- **📧 邮件测试**: http://localhost:8025
- **🗄️ 数据库管理**: http://localhost:8080
- **🔴 Redis 管理**: http://localhost:8081

## 🛠️ 开发工具

### 管理脚本
```bash
# 查看所有可用命令
./scripts/dev.sh help

# 常用命令
./scripts/dev.sh start      # 启动环境
./scripts/dev.sh stop       # 停止环境
./scripts/dev.sh logs       # 查看日志
./scripts/dev.sh shell      # 进入容器
./scripts/dev.sh db:setup   # 初始化数据库
```

### 开发命令
```bash
# Rails 相关
./scripts/dev.sh rails console              # Rails 控制台
./scripts/dev.sh rails generate model User  # 生成模型
./scripts/dev.sh rails db:migrate           # 数据库迁移

# 调试和测试
./scripts/dev.sh test                       # 运行测试
./scripts/dev.sh logs rails                 # 查看 Rails 日志
```

## 📁 项目结构

```
chatwoot/
├── 📄 .env.development          # 环境变量模板
├── 🐳 docker-compose.dev.yml    # 开发环境配置
├── 🚀 start-dev.sh             # 快速启动脚本
├── 📁 scripts/
│   └── 🛠️ dev.sh               # 开发管理脚本
├── 📁 docker/
│   ├── 📁 postgres/init/        # 数据库初始化脚本
│   └── 📁 redis/               # Redis 配置
└── 📁 docs/
    └── 📖 DEVELOPMENT.md        # 详细开发文档
```

## 🔧 核心功能

### ✅ 已配置功能
- ✅ **代码热重载**: Ruby 和前端代码修改自动生效
- ✅ **国内镜像源**: 使用阿里云镜像，下载速度快
- ✅ **数据持久化**: 数据库和缓存数据自动保存
- ✅ **开发工具**: 数据库管理、Redis 管理、邮件测试
- ✅ **健康检查**: 自动监控服务状态
- ✅ **日志管理**: 统一的日志收集和查看
- ✅ **一键操作**: 简化的启动、停止、重启命令

### 🎯 开发体验优化
- **快速启动**: 一条命令启动完整环境
- **智能缓存**: Node.js 和 Ruby 依赖缓存
- **端口管理**: 清晰的端口映射和管理
- **错误处理**: 完善的错误提示和解决方案

## 📊 服务架构

### 核心服务
| 服务 | 端口 | 功能 | 状态监控 |
|------|------|------|----------|
| Rails | 3000 | 主应用 | ✅ 健康检查 |
| Vite | 3036 | 前端开发 | ✅ 热重载 |
| PostgreSQL | 5432 | 数据库 | ✅ 连接检查 |
| Redis | 6379 | 缓存 | ✅ 状态监控 |
| Sidekiq | - | 后台任务 | ✅ 进程监控 |

### 开发工具
| 工具 | 端口 | 用途 |
|------|------|------|
| MailHog | 8025 | 邮件测试和调试 |
| Adminer | 8080 | 数据库可视化管理 |
| Redis Commander | 8081 | Redis 数据管理 |

## 🔍 常见问题

### Q: 如何重置开发环境？
```bash
./scripts/dev.sh clean    # 清理所有数据
./scripts/dev.sh start    # 重新启动
./scripts/dev.sh db:setup # 重新初始化数据库
```

### Q: 如何查看特定服务的日志？
```bash
./scripts/dev.sh logs rails     # Rails 日志
./scripts/dev.sh logs postgres  # 数据库日志
./scripts/dev.sh logs redis     # Redis 日志
```

### Q: 如何进入容器进行调试？
```bash
./scripts/dev.sh shell rails     # 进入 Rails 容器
./scripts/dev.sh shell postgres  # 进入数据库容器
```

### Q: 端口被占用怎么办？
修改 `docker-compose.dev.yml` 中的端口映射，例如：
```yaml
ports:
  - "3001:3000"  # 将 3000 改为 3001
```

## 📚 详细文档

- **📖 完整开发指南**: [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)
- **🔧 配置说明**: 查看 `.env.development` 文件
- **🐳 Docker 配置**: 查看 `docker-compose.dev.yml` 文件

## 🤝 开发工作流

1. **启动环境**: `./start-dev.sh`
2. **修改代码**: 代码自动热重载
3. **数据库操作**: `./scripts/dev.sh db:migrate`
4. **运行测试**: `./scripts/dev.sh test`
5. **查看日志**: `./scripts/dev.sh logs`
6. **停止环境**: `./scripts/dev.sh stop`

## 🎉 开始开发

环境配置完成！现在您可以：

1. 🔧 修改代码并实时查看效果
2. 🗄️ 使用 Adminer 管理数据库
3. 📧 使用 MailHog 测试邮件功能
4. 🔍 使用 Redis Commander 查看缓存
5. 📊 通过日志监控应用状态

**祝您开发愉快！** 🚀

---

> 💡 **提示**: 如果遇到问题，请查看 [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md) 获取详细的故障排除指南。
