{"openapi": "3.0.4", "info": {"title": "Chatwoot", "description": "This is the API documentation for Chatwoot server.", "version": "1.1.0", "termsOfService": "https://www.chatwoot.com/terms-of-service/", "contact": {"email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://app.chatwoot.com/"}], "paths": {"/platform/api/v1/accounts": {"post": {"tags": ["Accounts"], "operationId": "create-an-account", "summary": "Create an Account", "description": "Create an Account", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/platform_account"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/accounts/{account_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Accounts"], "operationId": "get-details-of-an-account", "summary": "Get an account details", "description": "Get the details of an account", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/platform_account"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given account does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Accounts"], "operationId": "update-an-account", "summary": "Update an account", "description": "Update an account's attributes", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/platform_account"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Accounts"], "operationId": "delete-an-account", "summary": "Delete an Account", "description": "Delete an Account", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The account does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/accounts/{account_id}/account_users": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Account Users"], "operationId": "list-all-account-users", "summary": "List all Account Users", "description": "List all account users", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_user"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Account Users"], "operationId": "create-an-account-user", "summary": "Create an Account User", "description": "Create an Account User", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_user_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"account_id": {"type": "integer", "description": "The ID of the account"}, "user_id": {"type": "integer", "description": "The ID of the user"}, "role": {"type": "string", "description": "whether user is an administrator or agent"}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Account Users"], "operationId": "delete-an-account-user", "summary": "Delete an Account User", "description": "Delete an Account User", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The account does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/agent_bots": {"get": {"tags": ["AgentBots"], "operationId": "list-all-agent-bots", "summary": "List all AgentBots", "description": "List all agent bots available", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of agent bots", "items": {"$ref": "#/components/schemas/agent_bot"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["AgentBots"], "operationId": "create-an-agent-bot", "summary": "Create an Agent <PERSON>", "description": "Create an agent bot", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/platform_agent_bot_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/agent_bots/{id}": {"parameters": [{"$ref": "#/components/parameters/agent_bot_id"}], "get": {"tags": ["AgentBots"], "operationId": "get-details-of-a-single-agent-bot", "summary": "Get an agent bot details", "description": "Get the details of an agent bot", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given agent bot ID does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["AgentBots"], "operationId": "update-an-agent-bot", "summary": "Update an agent bot", "description": "Update an agent bot's attributes", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/platform_agent_bot_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["AgentBots"], "operationId": "delete-an-agent-bot", "summary": "Delete an AgentBot", "description": "Delete an AgentBot", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The agent bot does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/users": {"post": {"tags": ["Users"], "operationId": "create-a-user", "summary": "Create a User", "description": "Create a User", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/users/{id}": {"parameters": [{"$ref": "#/components/parameters/platform_user_id"}], "get": {"tags": ["Users"], "operationId": "get-details-of-a-user", "summary": "Get an user details", "description": "Get the details of an user", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given user does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Users"], "operationId": "update-a-user", "summary": "Update a user", "description": "Update a user's attributes", "security": [{"platformAppApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Users"], "operationId": "delete-a-user", "summary": "Delete a User", "description": "Delete a User", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The user does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/platform/api/v1/users/{id}/login": {"parameters": [{"$ref": "#/components/parameters/platform_user_id"}], "get": {"tags": ["Users"], "operationId": "get-sso-url-of-a-user", "summary": "Get User SSO Link", "description": "Get the sso link of a user", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "description": "SSO url to autenticate the user"}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given user does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}], "get": {"tags": ["Inbox API"], "operationId": "get-details-of-a-inbox", "summary": "Inbox details", "description": "Get the details of an inbox", "security": [], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_inbox"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given inbox does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}], "post": {"tags": ["Contacts API"], "operationId": "create-a-contact", "summary": "Create a contact", "description": "Create a contact", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_contact_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_contact"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}], "get": {"tags": ["Contacts API"], "operationId": "get-details-of-a-contact", "summary": "Get a contact", "description": "Get the details of a contact", "security": [], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_contact"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given contact does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Contacts API"], "operationId": "update-a-contact", "summary": "Update a contact", "description": "Update a contact's attributes", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_contact_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_contact"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}], "post": {"tags": ["Conversations API"], "operationId": "create-a-conversation", "summary": "Create a conversation", "description": "Create a conversation", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_conversation_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_conversation"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "get": {"tags": ["Conversations API"], "operationId": "list-all-contact-conversations", "summary": "List all conversations", "description": "List all conversations for the contact", "security": [], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of conversations", "items": {"$ref": "#/components/schemas/public_conversation"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}, {"$ref": "#/components/parameters/conversation_id"}], "get": {"tags": ["Conversations API"], "operationId": "get-single-conversation", "summary": "Get a single conversation", "description": "Retrieves the details of a specific conversation", "security": [], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_conversation"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/toggle_status": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversations API"], "operationId": "resolve-conversation", "summary": "Resolve a conversation", "description": "Marks a conversation as resolved", "security": [], "responses": {"200": {"description": "Conversation resolved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_conversation"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/toggle_typing": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversations API"], "operationId": "toggle-typing-status", "summary": "Toggle typing status", "description": "Toggles the typing status in a conversation", "security": [], "parameters": [{"name": "typing_status", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Typing status, either 'on' or 'off'"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"typing_status": {"type": "string", "enum": ["on", "off"], "description": "The typing status to set", "example": "on"}}}}}}, "responses": {"200": {"description": "Typing status toggled successfully"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/update_last_seen": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversations API"], "operationId": "update-last-seen", "summary": "Update last seen", "description": "Updates the last seen time of the contact in a conversation", "security": [], "responses": {"200": {"description": "Last seen updated successfully"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/messages": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Messages API"], "operationId": "create-a-message", "summary": "Create a message", "description": "Create a message", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_message_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_message"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "get": {"tags": ["Messages API"], "operationId": "list-all-converation-messages", "summary": "List all messages", "description": "List all messages in the conversation", "security": [], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of messages", "items": {"$ref": "#/components/schemas/public_message"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/messages/{message_id}": {"parameters": [{"$ref": "#/components/parameters/public_inbox_identifier"}, {"$ref": "#/components/parameters/public_contact_identifier"}, {"$ref": "#/components/parameters/conversation_id"}, {"$ref": "#/components/parameters/message_id"}], "patch": {"tags": ["Messages API"], "operationId": "update-a-message", "summary": "Update a message", "description": "Update a message", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_message_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/public_message"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/survey/responses/{conversation_uuid}": {"parameters": [{"$ref": "#/components/parameters/conversation_uuid"}], "get": {"tags": ["CSAT Survey Page"], "operationId": "get-csat-survey-page", "summary": "Get CSAT survey page", "description": "You can redirect the client to this URL, instead of implementing the CSAT survey component yourself.", "security": [], "responses": {"200": {"description": "Success"}}}}, "/api/v1/accounts/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Account"], "operationId": "get-account-details", "summary": "Get account details", "description": "Get the details of the current account", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_show_response"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Account not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Account"], "operationId": "update-account", "summary": "Update account", "description": "Update account details, settings, and custom attributes", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_update_payload"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/account_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_detail"}}}}, "401": {"description": "Unauthorized (requires administrator role)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Account not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/audit_logs": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["<PERSON><PERSON>"], "operationId": "get-account-audit-logs", "summary": "List Audit Logs in Account", "description": "Get Details of Audit Log entries for an Account. This endpoint is only available in Enterprise editions and requires the audit_logs feature to be enabled.", "security": [{"userApiKey": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"per_page": {"type": "integer", "description": "Number of items per page", "example": 15}, "total_entries": {"type": "integer", "description": "Total number of audit log entries", "example": 150}, "current_page": {"type": "integer", "description": "Current page number", "example": 1}, "audit_logs": {"type": "array", "description": "Array of audit log entries", "items": {"$ref": "#/components/schemas/audit_log"}}}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "Feature not enabled or not available in current plan", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/agent_bots": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Account AgentBots"], "operationId": "list-all-account-agent-bots", "summary": "List all AgentBots", "description": "List all agent bots available for the current account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of agent bots", "items": {"$ref": "#/components/schemas/agent_bot"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Account AgentBots"], "operationId": "create-an-account-agent-bot", "summary": "Create an Agent <PERSON>", "description": "Create an agent bot in the account", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/agent_bots/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/agent_bot_id"}], "get": {"tags": ["Account AgentBots"], "operationId": "get-details-of-a-single-account-agent-bot", "summary": "Get an agent bot details", "description": "Get the details of an agent bot in the account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given agent bot ID does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Account AgentBots"], "operationId": "update-an-account-agent-bot", "summary": "Update an agent bot", "description": "Update an agent bot's attributes", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Account AgentBots"], "operationId": "delete-an-account-agent-bot", "summary": "Delete an AgentBot", "description": "Delete an AgentBot from the account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The agent bot does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/agents": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Agents"], "operationId": "get-account-agents", "summary": "List Agents in Account", "description": "Get Details of Agents in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/components/schemas/agent"}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Agents"], "operationId": "add-new-agent-to-account", "summary": "Add a New Agent", "description": "Add a new Agent to Account", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/agents/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "patch": {"tags": ["Agents"], "operationId": "update-agent-in-account", "summary": "Update Agent in Account", "description": "Update an Agent in Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the agent to be updated."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent"}}}}, "404": {"description": "Agent not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Agents"], "operationId": "delete-agent-from-account", "summary": "Remove an Agent from Account", "description": "Remove an Agent from Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the agent to be deleted."}], "responses": {"200": {"description": "Success"}, "404": {"description": "Agent not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/canned_responses": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Canned Responses"], "operationId": "get-account-canned-response", "summary": "List all Canned Responses in an Account", "description": "Get Details of Canned Responses in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of all canned responses", "items": {"$ref": "#/components/schemas/canned_response"}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Canned Responses"], "operationId": "add-new-canned-response-to-account", "summary": "Add a New Canned Response", "description": "Add a new Canned Response to Account", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/canned_response_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/canned_response"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/canned_responses/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "patch": {"tags": ["Canned Responses"], "operationId": "update-canned-response-in-account", "summary": "Update Canned Response in Account", "description": "Update a Canned Response in Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the canned response to be updated."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/canned_response_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/canned_response"}}}}, "404": {"description": "Agent not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Canned Responses"], "operationId": "delete-canned-response-from-account", "summary": "Remove a Canned Response from Account", "description": "Remove a Canned Response from Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the canned response to be deleted"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Canned Response not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/custom_attribute_definitions": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Custom Attributes"], "operationId": "get-account-custom-attribute", "summary": "List all custom attributes in an account", "parameters": [{"name": "attribute_model", "in": "query", "schema": {"type": "string", "enum": ["0", "1"]}, "description": "conversation_attribute(0)/contact_attribute(1)", "required": true}], "description": "Get details of custom attributes in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of all custom attributes", "items": {"$ref": "#/components/schemas/custom_attribute"}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Custom Attributes"], "operationId": "add-new-custom-attribute-to-account", "summary": "Add a new custom attribute", "description": "Add a new custom attribute to account", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_attribute_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_attribute"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/custom_attribute_definitions/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the custom attribute", "required": true}], "get": {"tags": ["Custom Attributes"], "operationId": "get-details-of-a-single-custom-attribute", "summary": "Get a custom attribute details", "security": [{"userApiKey": []}], "description": "Get the details of a custom attribute in the account", "parameters": [{"$ref": "#/components/parameters/account_id"}, {"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the custom attribute to be updated."}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_attribute"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given attribute ID does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Custom Attributes"], "operationId": "update-custom-attribute-in-account", "summary": "Update custom attribute in Account", "description": "Update a custom attribute in account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the custom attribute to be updated."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_attribute_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_attribute"}}}}, "404": {"description": "Agent not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Custom Attributes"], "operationId": "delete-custom-attribute-from-account", "summary": "Remove a custom attribute from account", "description": "Remove a custom attribute from account", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the custom attribute to be deleted"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Custom attribute not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts": {"get": {"tags": ["Contacts"], "operationId": "contactList", "description": "Listing all the resolved contacts with pagination (Page size = 15). Resolved contacts are the ones with a value for identifier, email or phone number", "summary": "List Contacts", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/contact_sort_param"}, {"$ref": "#/components/parameters/page"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contacts_list_response"}}}}, "400": {"description": "Bad Request Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Contacts"], "operationId": "contactCreate", "description": "Create a new Contact", "summary": "Create Contact", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/extended_contact"}}}}, "400": {"description": "Bad Request Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "number"}, "description": "ID of the contact"}], "get": {"tags": ["Contacts"], "operationId": "contactDetails", "summary": "Show Contact", "security": [{"userApiKey": []}], "description": "Get a contact belonging to the account using ID", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_show_response"}}}}, "404": {"description": "Contact not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "put": {"tags": ["Contacts"], "operationId": "contactUpdate", "summary": "Update Contact", "security": [{"userApiKey": []}], "description": "Update a contact belonging to the account using ID", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_update_payload"}}}}, "responses": {"204": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_base"}}}}, "404": {"description": "Contact not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Contacts"], "operationId": "contactDelete", "summary": "Delete Contact", "security": [{"userApiKey": []}], "description": "Delete a contact belonging to the account using ID", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Contact not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/conversations": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "number"}, "description": "ID of the contact"}], "get": {"tags": ["Contacts"], "operationId": "contactConversations", "summary": "Contact Conversations", "description": "Get conversations associated with that contact", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "number"}, "description": "ID of the contact"}], "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_conversations_response"}}}}, "404": {"description": "Contact not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/labels": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "number"}, "description": "ID of the contact"}], "get": {"tags": ["Contact Labels"], "operationId": "list-all-labels-of-a-contact", "summary": "List Labels", "description": "Lists all the labels of a contact", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_labels"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Contact not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Contact Labels"], "operationId": "contact-add-labels", "summary": "Add Labels", "description": "Add labels to a contact. Note that this API would overwrite the existing list of labels associated to the conversation.", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["labels"], "properties": {"labels": {"type": "array", "description": "Array of labels (comma-separated strings)", "items": {"type": "string"}, "example": ["support", "billing"]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_labels"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Contact not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/search": {"get": {"tags": ["Contacts"], "operationId": "contactSearch", "description": "Search the resolved contacts using a search key, currently supports email search (Page size = 15). Resolved contacts are the ones with a value for identifier, email or phone number", "summary": "Search Contacts", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "q", "in": "query", "schema": {"type": "string"}, "description": "Search using contact `name`, `identifier`, `email` or `phone number`"}, {"$ref": "#/components/parameters/contact_sort_param"}, {"$ref": "#/components/parameters/page"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contacts_list_response"}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/filter": {"post": {"tags": ["Contacts"], "operationId": "contactFilter", "description": "Filter contacts with custom filter options and pagination", "summary": "Contact Filter", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "page", "in": "query", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "items": {"type": "object", "properties": {"attribute_key": {"type": "string", "description": "filter attribute name"}, "filter_operator": {"type": "string", "description": "filter operator name", "enum": ["equal_to", "not_equal_to", "contains", "does_not_contain"]}, "values": {"type": "array", "items": {"type": "string"}, "description": "array of the attribute values to filter"}, "query_operator": {"type": "string", "description": "query operator name", "enum": ["AND", "OR"]}}}, "example": [{"attribute_key": "name", "filter_operator": "equal_to", "values": ["en"], "query_operator": "AND"}, {"attribute_key": "country_code", "filter_operator": "equal_to", "values": ["us"], "query_operator": null}]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contacts_list_response"}}}}, "400": {"description": "Bad Request Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/contact_inboxes": {"post": {"tags": ["Contact"], "operationId": "contactInboxCreation", "description": "Create a contact inbox record for an inbox", "summary": "Create contact inbox", "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the contact", "required": true}], "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["inbox_id"], "properties": {"inbox_id": {"type": "number", "description": "The ID of the inbox", "example": 1}, "source_id": {"type": "string", "description": "Contact Inbox Source Id"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contact_inboxes"}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "Incorrect payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/contactable_inboxes": {"get": {"tags": ["Contact"], "operationId": "contactableInboxesGet", "description": "Get List of contactable Inboxes", "summary": "Get Contactable Inboxes", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the contact", "required": true}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contactable_inboxes_response"}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "Incorrect payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/automation_rules": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Automation Rule"], "operationId": "get-account-automation-rule", "summary": "List all automation rules in an account", "parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/page"}], "description": "Get details of automation rules in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/automation_rule"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Automation Rule"], "operationId": "add-new-automation-rule-to-account", "summary": "Add a new automation rule", "description": "Add a new automation rule to account", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/automation_rule_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/automation_rule"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/automation_rules/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the Automation Rule", "required": true}], "get": {"tags": ["Automation Rule"], "operationId": "get-details-of-a-single-automation-rule", "summary": "Get a automation rule details", "description": "Get the details of a automation rule in the account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the automation rule to be updated."}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/automation_rule"}, "example": {"payload": {"id": 90, "account_id": 1, "name": "add-label-bug-if-message-contains-bug", "description": "add-label-bug-if-message-contains-bug", "event_name": "message_created", "conditions": [{"values": ["incoming"], "attribute_key": "message_type", "query_operator": "and", "filter_operator": "equal_to"}, {"values": ["bug"], "attribute_key": "content", "filter_operator": "contains"}], "actions": [{"action_name": "add_label", "action_params": ["bugs", "support-query"]}], "created_on": **********, "active": true}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given rule ID does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Automation Rule"], "operationId": "update-automation-rule-in-account", "summary": "Update automation rule in Account", "description": "Update a automation rule in account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the automation rule to be updated."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/automation_rule_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/automation_rule"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Rule not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Automation Rule"], "operationId": "delete-automation-rule-from-account", "summary": "Remove a automation rule from account", "description": "Remove a automation rule from account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the automation rule to be deleted"}], "responses": {"200": {"description": "Success"}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "automation rule not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/portals": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "post": {"tags": ["Help Center"], "operationId": "add-new-portal-to-account", "summary": "Add a new portal", "description": "Add a new portal to account", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/portal_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/portal"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "get": {"tags": ["Help Center"], "operationId": "get-portal", "summary": "List all portals in an account", "parameters": [{"$ref": "#/components/parameters/account_id"}], "description": "Get details of portals in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/portal"}, "example": {"payload": [{"id": 4, "color": "#1F93FF", "custom_domain": "chatwoot.help", "header_text": "Handbook", "homepage_link": "https://www.chatwoot.com", "name": "Handbook", "page_title": "Handbook", "slug": "handbook", "archived": false, "account_id": 1, "config": {"allowed_locales": [{"code": "en", "articles_count": 32, "categories_count": 9}]}, "inbox": {"id": 37, "avatar_url": "https://example.com/avatar.png", "channel_id": 1, "name": "Chatwoot", "channel_type": "Channel::WebWidget", "greeting_enabled": true, "widget_color": "#1F93FF", "website_url": "chatwoot.com"}, "logo": {"id": ********, "portal_id": 4, "file_type": "image/png", "account_id": 1, "file_url": "https://example.com/logo.png", "blob_id": ********, "filename": "square.png"}, "meta": {"all_articles_count": 0, "categories_count": 9, "default_locale": "en"}}]}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/portals/{id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/portal_id"}], "patch": {"tags": ["Help Center"], "operationId": "update-portal-to-account", "summary": "Update a portal", "description": "Update a portal to account", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/portal_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/portal_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/portal_single"}, "example": {"payload": {"id": 123, "archived": false, "color": "#1F93FF", "config": {"allowed_locales": [{"code": "en", "articles_count": 32, "categories_count": 9}]}, "custom_domain": "chatwoot.help", "header_text": "Handbook", "homepage_link": "https://www.chatwoot.com", "name": "Handbook", "slug": "handbook", "page_title": "Handbook", "account_id": 123, "inbox": {"id": 123, "name": "Chatwoot", "website_url": "chatwoot.com", "channel_type": "Channel::WebWidget", "avatar_url": "https://example.com/avatar.png", "widget_color": "#1F93FF", "website_token": "4cWzuf9i9jxN9tbnv8K9STKU", "enable_auto_assignment": true, "web_widget_script": "<script>...</script>", "welcome_title": "Hi there ! 🙌🏼", "welcome_tagline": "We make it simple to connect with us.", "greeting_enabled": true, "greeting_message": "Hey there 👋, Thank you for reaching out to us.", "channel_id": 123, "working_hours_enabled": true, "enable_email_collect": true, "csat_survey_enabled": true, "timezone": "America/Los_Angeles", "business_name": "Chatwoot", "hmac_mandatory": true}, "logo": {"id": 123, "portal_id": 123, "file_type": "image/png", "account_id": 123, "file_url": "https://example.com/logo.png", "blob_id": 123, "filename": "square.png"}, "meta": {"all_articles_count": 32, "categories_count": 9, "default_locale": "en"}}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Portal not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/portals/{id}/categories": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/portal_id"}], "post": {"tags": ["Help Center"], "operationId": "add-new-category-to-account", "summary": "Add a new category", "description": "Add a new category to portal", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/category_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/category"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/portals/{id}/articles": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/portal_id"}], "post": {"tags": ["Help Center"], "operationId": "add-new-article-to-account", "summary": "Add a new article", "description": "Add a new article to portal", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/article_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/article"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/meta": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Conversations"], "operationId": "conversationListMeta", "description": "Get open, unassigned and all Conversation counts", "summary": "Get Conversation Counts", "security": [{"userApiKey": []}], "parameters": [{"name": "status", "in": "query", "schema": {"type": "string", "enum": ["all", "open", "resolved", "pending", "snoozed"], "default": "open"}, "description": "Filter by conversation status."}, {"name": "q", "in": "query", "schema": {"type": "string"}, "description": "Filters conversations with messages containing the search term"}, {"name": "inbox_id", "in": "query", "schema": {"type": "integer"}}, {"name": "team_id", "in": "query", "schema": {"type": "integer"}}, {"name": "labels", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"meta": {"type": "object", "properties": {"mine_count": {"type": "number"}, "unassigned_count": {"type": "number"}, "assigned_count": {"type": "number"}, "all_count": {"type": "number"}}}}}}}}, "400": {"description": "Bad Request Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Conversations"], "operationId": "conversationList", "description": "List all the conversations with pagination", "summary": "Conversations List", "security": [{"userApiKey": []}], "parameters": [{"name": "assignee_type", "in": "query", "schema": {"type": "string", "enum": ["me", "unassigned", "all", "assigned"], "default": "all"}, "description": "Filter conversations by assignee type."}, {"name": "status", "in": "query", "schema": {"type": "string", "enum": ["all", "open", "resolved", "pending", "snoozed"], "default": "open"}, "description": "Filter by conversation status."}, {"name": "q", "in": "query", "schema": {"type": "string"}, "description": "Filters conversations with messages containing the search term"}, {"name": "inbox_id", "in": "query", "schema": {"type": "integer"}}, {"name": "team_id", "in": "query", "schema": {"type": "integer"}}, {"name": "labels", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "paginate through conversations"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_list"}}}}, "400": {"description": "Bad Request Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Conversations"], "operationId": "newConversation", "summary": "Create New Conversation", "description": "Creating a conversation in chatwoot requires a source id. \n\n Learn more about source_id: https://www.chatwoot.com/hc/user-guide/articles/**********-how-to-create-an-api-channel-inbox#send-messages-to-the-api-channel", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the conversation"}, "account_id": {"type": "number", "description": "Account Id"}, "inbox_id": {"type": "number", "description": "ID of the inbox"}}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/filter": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "post": {"tags": ["Conversations"], "operationId": "conversationFilter", "description": "Filter conversations with custom filter options and pagination", "summary": "Conversations Filter", "security": [{"userApiKey": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "items": {"type": "object", "properties": {"attribute_key": {"type": "string", "description": "filter attribute name"}, "filter_operator": {"type": "string", "description": "filter operator name", "enum": ["equal_to", "not_equal_to", "contains", "does_not_contain"]}, "values": {"type": "array", "items": {"type": "string"}, "description": "array of the attribute values to filter"}, "query_operator": {"type": "string", "description": "query operator name", "enum": ["AND", "OR"]}}}, "example": [{"attribute_key": "browser_language", "filter_operator": "not_equal_to", "values": ["en"], "query_operator": "AND"}, {"attribute_key": "status", "filter_operator": "equal_to", "values": ["pending"], "query_operator": null}]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_list"}}}}, "400": {"description": "Bad Request Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "get": {"tags": ["Conversations"], "operationId": "get-details-of-a-conversation", "summary": "Conversation Details", "security": [{"userApiKey": []}], "description": "Get all details regarding a conversation with all messages in the conversation", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_show"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Conversations"], "operationId": "update-conversation", "summary": "Update Conversation", "description": "Update Conversation Attributes", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"priority": {"type": "string", "enum": ["urgent", "high", "medium", "low", "none"], "description": "The priority of the conversation", "example": "high"}, "sla_policy_id": {"type": "number", "description": "The ID of the SLA policy (Available only in Enterprise edition)", "example": 1}}}}}}, "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/toggle_status": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversations"], "operationId": "toggle-status-of-a-conversation", "summary": "Toggle Status", "description": "Toggles the status of the conversation between open and resolved", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["open", "resolved", "pending"], "description": "The status of the conversation", "example": "open"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_status_toggle"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/toggle_priority": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversations"], "operationId": "toggle-priority-of-a-conversation", "summary": "Toggle Priority", "description": "Toggles the priority of conversation", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["priority"], "properties": {"priority": {"type": "string", "enum": ["urgent", "high", "medium", "low", "none"], "description": "The priority of the conversation", "example": "high"}}}}}}, "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/custom_attributes": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversations"], "operationId": "update-custom-attributes-of-a-conversation", "summary": "Update Custom Attributes", "description": "Updates the custom attributes of a conversation", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["custom_attributes"], "properties": {"custom_attributes": {"type": "object", "description": "The custom attributes to be set for the conversation", "example": {"order_id": "12345", "previous_conversation": "67890"}}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"custom_attributes": {"type": "object", "description": "The custom attributes of the conversation"}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/assignments": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "post": {"tags": ["Conversation Assignments"], "operationId": "assign-a-conversation", "summary": "Assign Conversation", "description": "Assign a conversation to an agent or a team", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"assignee_id": {"type": "number", "description": "Id of the assignee user", "example": 1}, "team_id": {"type": "number", "description": "Id of the team. If the assignee_id is present, this param would be ignored", "example": 1}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/labels": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "get": {"tags": ["Conversations"], "operationId": "list-all-labels-of-a-conversation", "summary": "List Labels", "security": [{"userApiKey": []}], "description": "Lists all the labels of a conversation", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_labels"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Conversations"], "operationId": "conversation-add-labels", "summary": "Add Labels", "security": [{"userApiKey": []}], "description": "Add labels to a conversation. Note that this API would overwrite the existing list of labels associated to the conversation.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["labels"], "properties": {"labels": {"type": "array", "description": "Array of labels (comma-separated strings)", "items": {"type": "string"}, "example": ["support", "billing"]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_labels"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inboxes": {"get": {"tags": ["Inboxes"], "operationId": "listAllInboxes", "summary": "List all inboxes", "description": "List all inboxes available in the current account", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of inboxes", "items": {"$ref": "#/components/schemas/inbox"}}}}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}/": {"get": {"tags": ["Inboxes"], "operationId": "GetInbox", "summary": "Get an inbox", "security": [{"userApiKey": []}], "description": "Get an inbox available in the current account", "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the inbox", "required": true}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inbox"}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inboxes/": {"post": {"tags": ["Inboxes"], "operationId": "inboxCreation", "summary": "Create an inbox", "description": "You can create more than one website inbox in each account", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inbox_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inbox"}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}": {"patch": {"tags": ["Inboxes"], "operationId": "updateInbox", "summary": "Update Inbox", "security": [{"userApiKey": []}], "description": "Update an existing inbox", "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the inbox", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inbox_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inbox"}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}/agent_bot": {"get": {"tags": ["Inboxes"], "operationId": "getInboxAgentBot", "summary": "Show Inbox Agent <PERSON>", "description": "See if an agent bot is associated to the Inbox", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the inbox", "required": true}], "responses": {"204": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/agent_bot"}}}}, "404": {"description": "Inbox not found, Agent bot not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}/set_agent_bot": {"post": {"tags": ["Inboxes"], "operationId": "updateAgentBot", "summary": "Add or remove agent bot", "security": [{"userApiKey": []}], "description": "To add an agent bot pass agent_bot id, to remove agent bot from an inbox pass null", "parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "id", "in": "path", "schema": {"type": "number"}, "description": "ID of the inbox", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["agent_bot"], "properties": {"agent_bot": {"type": "number", "description": "Agent bot ID", "example": 1}}}}}}, "responses": {"204": {"description": "Success"}, "404": {"description": "Inbox not found, Agent bot not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inbox_members/{inbox_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/inbox_id"}], "get": {"tags": ["Inboxes"], "operationId": "get-inbox-members", "summary": "List Agents in Inbox", "description": "Get Details of Agents in an Inbox", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/inbox_id"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/components/schemas/agent"}}}}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/inbox_members": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "post": {"tags": ["Inboxes"], "operationId": "add-new-agent-to-inbox", "summary": "Add a New Agent", "description": "Add a new Agent to Inbox", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["inbox_id", "user_ids"], "properties": {"inbox_id": {"type": "integer", "description": "The ID of the inbox", "example": 1}, "user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the inbox", "example": [1]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/components/schemas/agent"}}}}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "User must exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Inboxes"], "operationId": "update-agents-in-inbox", "summary": "Update Agents in Inbox", "description": "All agents except the one passed in params will be removed", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["inbox_id", "user_ids"], "properties": {"inbox_id": {"type": "string", "description": "The ID of the inbox", "example": 1}, "user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the inbox", "example": [1]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/components/schemas/agent"}}}}}}}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "User must exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Inboxes"], "operationId": "delete-agent-in-inbox", "summary": "Remove an Agent from Inbox", "description": "Remove an Agent from Inbox", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["inbox_id", "user_ids"], "properties": {"inbox_id": {"type": "string", "description": "The ID of the inbox"}, "user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be deleted from the inbox"}}}}}}, "responses": {"200": {"description": "Success"}, "404": {"description": "Inbox not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "User must exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/messages": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}], "get": {"tags": ["Messages"], "operationId": "list-all-messages", "summary": "Get messages", "security": [{"userApiKey": []}], "description": "List all messages of a conversation", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"meta": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "string"}}, "additional_attributes": {"type": "object"}, "contact": {"$ref": "#/components/schemas/contact"}, "assignee": {"$ref": "#/components/schemas/agent"}, "agent_last_seen_at": {"type": "string", "format": "date-time"}, "assignee_last_seen_at": {"type": "string", "format": "date-time"}}}, "payload": {"type": "array", "description": "Array of messages", "items": {"$ref": "#/components/schemas/message"}}}}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Messages"], "operationId": "create-a-new-message-in-a-conversation", "summary": "Create New Message", "description": "Create a new message in the conversation.\n\n## WhatsApp Template Messages\n\nFor WhatsApp channels, you can send structured template messages using the `template_params` field. \nTemplates must be pre-approved in WhatsApp Business Manager.\n\n### Example Templates\n\n**Text with Image Header:**\n```json\n{\n  \"content\": \"Hi your order 121212 is confirmed. Please wait for further updates\",\n  \"template_params\": {\n    \"name\": \"order_confirmation\",\n    \"category\": \"MARKETING\",\n    \"language\": \"en\",\n    \"processed_params\": {\n      \"body\": {\n        \"1\": \"121212\"\n      },\n      \"header\": {\n        \"media_url\": \"https://picsum.photos/200/300\",\n        \"media_type\": \"image\"\n      }\n    }\n  }\n}\n```\n\n**Text with Copy Code Button:**\n```json\n{\n  \"content\": \"Special offer! Get 30% off your next purchase. Use the code below\",\n  \"template_params\": {\n    \"name\": \"discount_coupon\",\n    \"category\": \"MARKETING\",\n    \"language\": \"en\",\n    \"processed_params\": {\n      \"body\": {\n        \"discount_percentage\": \"30\"\n      },\n      \"buttons\": [{\n        \"type\": \"copy_code\",\n        \"parameter\": \"SAVE20\"\n      }]\n    }\n  }\n}\n```\n", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_message_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/generic_id"}, {"$ref": "#/components/schemas/message"}]}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/messages/{message_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/conversation_id"}, {"$ref": "#/components/parameters/message_id"}], "delete": {"tags": ["Messages"], "operationId": "delete-a-message", "summary": "Delete a message", "security": [{"userApiKey": []}], "description": "Delete a message and it's attachments from the conversation.", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The message or conversation does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/integrations/apps": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Integrations"], "operationId": "get-details-of-all-integrations", "summary": "List all the Integrations", "security": [{"userApiKey": []}], "description": "Get the details of all Integrations available for the account", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of Integration apps", "items": {"$ref": "#/components/schemas/integrations_app"}}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Url not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/integrations/hooks": {"post": {"tags": ["Integrations"], "operationId": "create-an-integration-hook", "summary": "Create an integration hook", "description": "Create an integration hook", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/integrations_hook_create_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/integrations_hook"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/integrations/hooks/{hook_id}": {"patch": {"tags": ["Integrations"], "operationId": "update-an-integrations-hook", "summary": "Update an Integration Hook", "description": "Update an Integration Hook", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/hook_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/integrations_hook_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/integrations_hook"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Integrations"], "operationId": "delete-an-integration-hook", "summary": "Delete an Integration Hook", "description": "Delete an Integration Hook", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/hook_id"}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The hook does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/profile": {"get": {"tags": ["Profile"], "operationId": "fetchProfile", "summary": "Fetch user profile", "description": "Get the user profile details", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/teams": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Teams"], "operationId": "list-all-teams", "summary": "List all teams", "security": [{"userApiKey": []}], "description": "List all teams available in the current account", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of teams", "items": {"$ref": "#/components/schemas/team"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Teams"], "operationId": "create-a-team", "summary": "Create a team", "security": [{"userApiKey": []}], "description": "Create a team in the account", "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/team_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/team"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/teams/{team_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/team_id"}], "get": {"tags": ["Teams"], "operationId": "get-details-of-a-single-team", "summary": "Get a team details", "security": [{"userApiKey": []}], "description": "Get the details of a team in the account", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/team"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given team ID does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Teams"], "operationId": "update-a-team", "summary": "Update a team", "security": [{"userApiKey": []}], "description": "Update a team's attributes", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/team_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/team"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Teams"], "operationId": "delete-a-team", "summary": "Delete a team", "security": [{"userApiKey": []}], "description": "Delete a team from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The team does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/teams/{team_id}/team_members": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/team_id"}], "get": {"tags": ["Teams"], "operationId": "get-team-members", "summary": "List Agents in Team", "description": "Get Details of Agents in an Team", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/team_id"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of all agents in the team", "items": {"$ref": "#/components/schemas/agent"}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Teams"], "operationId": "add-new-agent-to-team", "summary": "Add a New Agent", "description": "Add a new Agent to Team", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the team", "example": [1]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/components/schemas/agent"}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "User must exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Teams"], "operationId": "update-agents-in-team", "summary": "Update Agents in Team", "description": "All agents except the one passed in params will be removed", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the team", "example": [1]}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of all agents in the team", "items": {"$ref": "#/components/schemas/agent"}}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "User must exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Teams"], "operationId": "delete-agent-in-team", "summary": "Remove an Agent from Team", "description": "Remove an Agent from Team", "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be deleted from the team"}}}}}}, "responses": {"200": {"description": "Success"}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "Team not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "422": {"description": "User must exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/custom_filters": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"in": "query", "name": "filter_type", "schema": {"type": "string", "enum": ["conversation", "contact", "report"]}, "required": false, "description": "The type of custom filter"}], "get": {"tags": ["Custom Filters"], "operationId": "list-all-filters", "summary": "List all custom filters", "description": "List all custom filters in a category of a user", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of custom filters", "items": {"$ref": "#/components/schemas/custom_filter"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Custom Filters"], "operationId": "create-a-custom-filter", "summary": "Create a custom filter", "description": "Create a custom filter in the account", "parameters": [{"$ref": "#/components/parameters/account_id"}], "security": [{"userApiKey": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_filter_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_filter"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/custom_filters/{custom_filter_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/custom_filter_id"}], "get": {"tags": ["Custom Filters"], "operationId": "get-details-of-a-single-custom-filter", "summary": "Get a custom filter details", "description": "Get the details of a custom filter in the account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_filter"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The given team ID does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "patch": {"tags": ["Custom Filters"], "operationId": "update-a-custom-filter", "summary": "Update a custom filter", "security": [{"userApiKey": []}], "description": "Update a custom filter's attributes", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_filter_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/custom_filter"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Custom Filters"], "operationId": "delete-a-custom-filter", "summary": "Delete a custom filter", "security": [{"userApiKey": []}], "description": "Delete a custom filter from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "404": {"description": "The custom filter does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/webhooks": {"parameters": [{"$ref": "#/components/parameters/account_id"}], "get": {"tags": ["Webhooks"], "operationId": "list-all-webhooks", "summary": "List all webhooks", "security": [{"userApiKey": []}], "description": "List all webhooks in the account", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of webhook objects", "items": {"$ref": "#/components/schemas/webhook"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "post": {"tags": ["Webhooks"], "operationId": "create-a-webhook", "summary": "Add a webhook", "security": [{"userApiKey": []}], "description": "Add a webhook subscription to the account", "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/webhook_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/webhook"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v1/accounts/{account_id}/webhooks/{webhook_id}": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/webhook_id"}], "patch": {"tags": ["Webhooks"], "operationId": "update-a-webhook", "summary": "Update a webhook object", "security": [{"userApiKey": []}], "description": "Update a webhook object in the account", "parameters": [{"$ref": "#/components/parameters/account_id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/webhook_create_update_payload"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/webhook"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}, "delete": {"tags": ["Webhooks"], "operationId": "delete-a-webhook", "summary": "Delete a webhook", "security": [{"userApiKey": []}], "description": "Delete a webhook from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "The webhook does not exist in the account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v2/accounts/{account_id}/reports": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/report_metric"}, {"$ref": "#/components/parameters/report_type"}, {"in": "query", "name": "id", "schema": {"type": "string"}, "description": "The Id of specific object in case of agent/inbox/label"}, {"in": "query", "name": "since", "schema": {"type": "string"}, "description": "The timestamp from where report should start."}, {"in": "query", "name": "until", "schema": {"type": "string"}, "description": "The timestamp from where report should stop."}], "get": {"tags": ["Reports"], "operationId": "list-all-conversation-statistics", "summary": "Get Account reports", "security": [{"userApiKey": []}], "description": "Get Account reports for a specific type, metric and date range", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of date based conversation statistics", "items": {"type": "object", "properties": {"value": {"type": "string"}, "timestamp": {"type": "number"}}}}}}}, "404": {"description": "reports not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v2/accounts/{account_id}/reports/summary": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"$ref": "#/components/parameters/report_type"}, {"in": "query", "name": "id", "schema": {"type": "string"}, "description": "The Id of specific object in case of agent/inbox/label"}, {"in": "query", "name": "since", "schema": {"type": "string"}, "description": "The timestamp from where report should start."}, {"in": "query", "name": "until", "schema": {"type": "string"}, "description": "The timestamp from where report should stop."}], "get": {"tags": ["Reports"], "operationId": "list-all-conversation-statistics-summary", "summary": "Get Account reports summary", "security": [{"userApiKey": []}], "description": "Get Account reports summary for a specific type and date range", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/account_summary"}}}}, "404": {"description": "reports not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v2/accounts/{account_id}/reports/conversations": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"in": "query", "name": "type", "schema": {"type": "string", "enum": ["account"]}, "required": true, "description": "Type of report"}], "get": {"tags": ["Reports"], "operationId": "get-account-conversation-metrics", "summary": "Account Conversation Metrics", "security": [{"userApiKey": []}], "description": "Get conversation metrics for Account", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "description": "Object of account conversation metrics", "properties": {"open": {"type": "number"}, "unattended": {"type": "number"}, "unassigned": {"type": "number"}}}}}}, "404": {"description": "reports not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/api/v2/accounts/{account_id}/reports/conversations/": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"in": "query", "name": "type", "schema": {"type": "string", "enum": ["agent"]}, "required": true, "description": "Type of report"}, {"in": "query", "name": "user_id", "schema": {"type": "string"}, "description": "The numeric ID of the user"}], "get": {"tags": ["Reports"], "operationId": "get-agent-conversation-metrics", "summary": "Agent Conversation Metrics", "security": [{"userApiKey": []}], "description": "Get conversation metrics for Agent", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "description": "Array of agent based conversation metrics", "items": {"$ref": "#/components/schemas/agent_conversation_metrics"}}}}}, "404": {"description": "reports not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}, "403": {"description": "Access denied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/bad_request_error"}}}}}}}, "/accounts/{account_id}/conversations/{conversation_id}/messages": {"parameters": [{"$ref": "#/components/parameters/account_id"}, {"name": "conversation_id", "in": "path", "description": "ID of the conversation", "required": true, "schema": {"type": "number"}}], "get": {"tags": ["Conversation"], "summary": "Get messages from a conversation", "description": "Returns all messages from a specific conversation", "operationId": "getConversationMessages", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/conversation_messages"}}}}}}}}, "components": {"schemas": {"bad_request_error": {"title": "data", "type": "object", "properties": {"description": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/request_error"}}}}, "request_error": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}, "code": {"type": "string"}}}, "generic_id": {"type": "object", "properties": {"id": {"type": "number"}}}, "canned_response": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID of the canned response"}, "account_id": {"type": "integer", "description": "Account Id"}, "short_code": {"type": "string", "description": "Short Code for quick access of the canned response"}, "content": {"type": "string", "description": "Message content for canned response"}, "created_at": {"type": "string", "description": "The date and time when the canned response was created"}, "updated_at": {"type": "string", "description": "The date and time when the canned response was updated"}}}, "custom_attribute": {"type": "object", "properties": {"id": {"type": "integer", "description": "Identifier"}, "attribute_display_name": {"type": "string", "description": "Attribute display name"}, "attribute_display_type": {"type": "string", "description": "Attribute display type (text, number, currency, percent, link, date, list, checkbox)"}, "attribute_description": {"type": "string", "description": "Attribute description"}, "attribute_key": {"type": "string", "description": "Attribute unique key value"}, "regex_pattern": {"type": "string", "description": "Regex pattern"}, "regex_cue": {"type": "string", "description": "Regex cue"}, "attribute_values": {"type": "string", "description": "Attribute values"}, "attribute_model": {"type": "string", "description": "Attribute type(conversation_attribute/contact_attribute)"}, "default_value": {"type": "string", "description": "Attribute default value"}, "created_at": {"type": "string", "description": "The date and time when the custom attribute was created"}, "updated_at": {"type": "string", "description": "The date and time when the custom attribute was updated"}}}, "automation_rule": {"type": "object", "properties": {"payload": {"description": "Response payload that contains automation rule(s)", "oneOf": [{"type": "array", "description": "Array of automation rules (for listing endpoint)", "items": {"$ref": "#/components/schemas/automation_rule_item"}}, {"type": "object", "description": "Single automation rule (for show/create/update endpoints)", "allOf": [{"$ref": "#/components/schemas/automation_rule_item"}]}]}}}, "automation_rule_item": {"type": "object", "properties": {"id": {"type": "integer", "description": "The ID of the automation rule"}, "account_id": {"type": "integer", "description": "Account Id"}, "name": {"type": "string", "description": "The name of the rule", "example": "Add label on message create event"}, "description": {"type": "string", "description": "Description to give more context about the rule", "example": "Add label support and sales on message create event if incoming message content contains text help"}, "event_name": {"type": "string", "description": "Automation Rule event, on which we call the actions(conversation_created, conversation_updated, message_created)", "enum": ["conversation_created", "conversation_updated", "message_created"], "example": "message_created"}, "conditions": {"type": "array", "description": "Array of conditions on which conversation/message filter would work", "items": {"type": "object", "properties": {"values": {"type": "array", "items": {"type": "string"}}, "attribute_key": {"type": "string"}, "query_operator": {"type": "string"}, "filter_operator": {"type": "string"}}, "example": {"attribute_key": "content", "filter_operator": "contains", "values": ["help"], "query_operator": "and"}}}, "actions": {"type": "array", "description": "Array of actions which we perform when condition matches", "items": {"type": "object", "properties": {"action_name": {"type": "string"}, "action_params": {"type": "array", "items": {"type": "string"}}}, "example": {"action_name": "add_label", "action_params": ["support", "sales"]}}}, "created_on": {"type": "integer", "description": "The timestamp when the rule was created"}, "active": {"type": "boolean", "description": "Enable/disable automation rule"}}}, "portal": {"type": "object", "properties": {"payload": {"type": "array", "items": {"$ref": "#/components/schemas/portal_item"}}}}, "portal_single": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/portal_item"}}}, "portal_config": {"type": "object", "description": "Configuration settings for the portal", "properties": {"allowed_locales": {"type": "array", "description": "List of allowed locales for the portal", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "The language code"}, "articles_count": {"type": "integer", "description": "Number of articles in this locale"}, "categories_count": {"type": "integer", "description": "Number of categories in this locale"}}}}}}, "portal_logo": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID of the logo file"}, "portal_id": {"type": "integer", "description": "ID of the portal this logo belongs to"}, "file_type": {"type": "string", "description": "MIME type of the file"}, "account_id": {"type": "integer", "description": "ID of the account"}, "file_url": {"type": "string", "description": "URL to access the logo file"}, "blob_id": {"type": "integer", "description": "ID of the blob"}, "filename": {"type": "string", "description": "Name of the file"}}}, "portal_meta": {"type": "object", "properties": {"all_articles_count": {"type": "integer", "description": "Total number of articles"}, "archived_articles_count": {"type": "integer", "nullable": true, "description": "Number of archived articles"}, "published_count": {"type": "integer", "nullable": true, "description": "Number of published articles"}, "draft_articles_count": {"type": "integer", "nullable": true, "description": "Number of draft articles"}, "categories_count": {"type": "integer", "description": "Number of categories"}, "default_locale": {"type": "string", "description": "Default locale for the portal"}}}, "portal_item": {"type": "object", "properties": {"id": {"type": "integer", "description": "The ID of the portal"}, "archived": {"type": "boolean", "description": "Whether the portal is archived"}, "color": {"type": "string", "description": "The color code for the portal"}, "config": {"$ref": "#/components/schemas/portal_config"}, "custom_domain": {"type": "string", "description": "Custom domain for the portal"}, "header_text": {"type": "string", "description": "The header text for the portal"}, "homepage_link": {"type": "string", "description": "Homepage link for the portal"}, "name": {"type": "string", "description": "Name of the portal"}, "slug": {"type": "string", "description": "URL slug for the portal"}, "page_title": {"type": "string", "description": "Page title for the portal"}, "account_id": {"type": "integer", "description": "ID of the account the portal belongs to"}, "inbox": {"$ref": "#/components/schemas/inbox"}, "logo": {"$ref": "#/components/schemas/portal_logo"}, "meta": {"$ref": "#/components/schemas/portal_meta"}}}, "category": {"type": "object", "properties": {"id": {"type": "integer"}, "description": {"type": "string", "description": "The text content."}, "locale": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "position": {"type": "integer"}, "portal_id": {"type": "integer"}, "account_id": {"type": "integer"}, "associated_category_id": {"type": "integer", "description": "To associate similar categories to each other, e.g same category of product documentation in different languages"}, "parent_category_id": {"type": "integer", "description": "To define parent category, e.g product documentation has multiple level features in sales category or in engineering category."}}}, "article": {"type": "object", "properties": {"id": {"type": "integer"}, "content": {"type": "string", "description": "The text content."}, "meta": {"type": "object"}, "position": {"type": "integer"}, "status": {"type": "integer", "enum": ["draft", "published", "archived"]}, "title": {"type": "string"}, "slug": {"type": "string"}, "views": {"type": "integer"}, "portal_id": {"type": "integer"}, "account_id": {"type": "integer"}, "author_id": {"type": "integer"}, "category_id": {"type": "integer"}, "folder_id": {"type": "integer"}, "associated_article_id": {"type": "integer", "description": "To associate similar articles to each other, e.g to provide the link for the reference."}}}, "contact": {"type": "object", "properties": {"payload": {"type": "array", "items": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The object containing additional attributes related to the contact"}, "availability_status": {"type": "string", "description": "The availability status of the contact"}, "email": {"type": "string", "description": "The email address of the contact"}, "id": {"type": "integer", "description": "The ID of the contact"}, "name": {"type": "string", "description": "The name of the contact"}, "phone_number": {"type": "string", "description": "The phone number of the contact"}, "blocked": {"type": "boolean", "description": "Whether the contact is blocked"}, "identifier": {"type": "string", "description": "The identifier of the contact"}, "thumbnail": {"type": "string", "description": "The thumbnail of the contact"}, "custom_attributes": {"type": "object", "description": "The custom attributes of the contact", "example": {"attribute_key": "attribute_value", "signed_up_at": "dd/mm/yyyy"}}, "last_activity_at": {"type": "integer", "description": "The last activity at of the contact"}, "created_at": {"type": "integer", "description": "The created at of the contact"}, "contact_inboxes": {"type": "array", "items": {"$ref": "#/components/schemas/contact_inboxes"}}}}}}}, "conversation": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the conversation"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/message"}}, "account_id": {"type": "number", "description": "Account Id"}, "uuid": {"type": "string", "description": "UUID of the conversation"}, "additional_attributes": {"type": "object", "description": "The object containing additional attributes related to the conversation"}, "agent_last_seen_at": {"type": "number", "description": "The last activity at of the agent"}, "assignee_last_seen_at": {"type": "number", "description": "The last activity at of the assignee"}, "can_reply": {"type": "boolean", "description": "Whether the conversation can be replied to"}, "contact_last_seen_at": {"type": "number", "description": "The last activity at of the contact"}, "custom_attributes": {"type": "object", "description": "The object to save custom attributes for conversation, accepts custom attributes key and value"}, "inbox_id": {"type": "number", "description": "ID of the inbox"}, "labels": {"type": "array", "items": {"type": "string"}, "description": "The labels of the conversation"}, "muted": {"type": "boolean", "description": "Whether the conversation is muted"}, "snoozed_until": {"type": "number", "description": "The time at which the conversation will be unmuted"}, "status": {"type": "string", "enum": ["open", "resolved", "pending"], "description": "The status of the conversation"}, "created_at": {"type": "number", "description": "The time at which conversation was created"}, "updated_at": {"type": "number", "description": "The time at which conversation was updated"}, "timestamp": {"type": "string", "description": "The time at which conversation was created"}, "first_reply_created_at": {"type": "number", "description": "The time at which the first reply was created"}, "unread_count": {"type": "number", "description": "The number of unread messages"}, "last_non_activity_message": {"$ref": "#/components/schemas/message"}, "last_activity_at": {"type": "number", "description": "The last activity at of the conversation"}, "priority": {"type": "string", "description": "The priority of the conversation"}, "waiting_since": {"type": "number", "description": "The time at which the conversation was waiting"}, "sla_policy_id": {"type": "number", "description": "The ID of the SLA policy"}, "applied_sla": {"type": "object", "description": "The applied SLA"}, "sla_events": {"type": "array", "items": {"type": "object", "description": "SLA event objects"}}}}, "message": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the message"}, "content": {"type": "string", "description": "The text content of the message"}, "account_id": {"type": "number", "description": "The ID of the account"}, "inbox_id": {"type": "number", "description": "The ID of the inbox"}, "conversation_id": {"type": "number", "description": "The ID of the conversation"}, "message_type": {"type": "integer", "enum": [0, 1, 2], "description": "The type of the message"}, "created_at": {"type": "integer", "description": "The time at which message was created"}, "updated_at": {"type": "integer", "description": "The time at which message was updated"}, "private": {"type": "boolean", "description": "The flags which shows whether the message is private or not"}, "status": {"type": "string", "enum": ["sent", "delivered", "read", "failed"], "description": "The status of the message"}, "source_id": {"type": "string", "description": "The source ID of the message"}, "content_type": {"type": "string", "enum": ["text", "input_select", "cards", "form"], "description": "The type of the template message"}, "content_attributes": {"type": "object", "description": "The content attributes for each content_type"}, "sender_type": {"type": "string", "enum": ["contact", "agent", "agent_bot"], "description": "The type of the sender"}, "sender_id": {"type": "number", "description": "The ID of the sender"}, "external_source_ids": {"type": "object", "description": "The external source IDs of the message"}, "additional_attributes": {"type": "object", "description": "The additional attributes of the message"}, "processed_message_content": {"type": "string", "description": "The processed message content"}, "sentiment": {"type": "object", "description": "The sentiment of the message"}, "conversation": {"type": "object", "description": "The conversation object"}, "attachment": {"type": "object", "description": "The file object attached to the image"}, "sender": {"type": "object", "description": "User/Agent/AgentBot object"}}}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "access_token": {"type": "string"}, "account_id": {"type": "number"}, "available_name": {"type": "string"}, "avatar_url": {"type": "string"}, "confirmed": {"type": "boolean"}, "display_name": {"type": "string", "nullable": true}, "message_signature": {"type": "string", "nullable": true}, "email": {"type": "string"}, "hmac_identifier": {"type": "string"}, "inviter_id": {"type": "number"}, "name": {"type": "string"}, "provider": {"type": "string"}, "pubsub_token": {"type": "string"}, "role": {"type": "string", "enum": ["agent", "administrator"]}, "ui_settings": {"type": "object"}, "uid": {"type": "string"}, "type": {"type": "string", "nullable": true}, "custom_attributes": {"type": "object", "description": "Available for users who are created through platform APIs and has custom attributes associated."}, "accounts": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "status": {"type": "string"}, "active_at": {"type": "string", "format": "date-time"}, "role": {"type": "string", "enum": ["administrator", "agent"]}, "permissions": {"type": "array", "items": {"type": "string"}}, "availability": {"type": "string"}, "availability_status": {"type": "string"}, "auto_offline": {"type": "boolean"}, "custom_role_id": {"type": "number", "nullable": true}, "custom_role": {"type": "object", "nullable": true}}}}}}, "agent": {"type": "object", "properties": {"id": {"type": "integer"}, "account_id": {"type": "integer"}, "availability_status": {"type": "string", "enum": ["available", "busy", "offline"], "description": "The availability status of the agent computed by <PERSON><PERSON><PERSON>ot."}, "auto_offline": {"type": "boolean", "description": "Whether the availability status of agent is configured to go offline automatically when away."}, "confirmed": {"type": "boolean", "description": "Whether the agent has confirmed their email address."}, "email": {"type": "string", "description": "The email of the agent"}, "available_name": {"type": "string", "description": "The available name of the agent"}, "name": {"type": "string", "description": "The name of the agent"}, "role": {"type": "string", "enum": ["agent", "administrator"], "description": "The role of the agent"}, "thumbnail": {"type": "string", "description": "The thumbnail of the agent"}, "custom_role_id": {"type": "integer", "description": "The custom role id of the agent"}}}, "inbox": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the inbox"}, "name": {"type": "string", "description": "The name of the inbox"}, "website_url": {"type": "string", "description": "Website URL"}, "channel_type": {"type": "string", "description": "The type of the inbox"}, "avatar_url": {"type": "string", "description": "The avatar image of the inbox"}, "widget_color": {"type": "string", "description": "Widget Color used for customization of the widget"}, "website_token": {"type": "string", "description": "Website Token"}, "enable_auto_assignment": {"type": "boolean", "description": "The flag which shows whether Auto Assignment is enabled or not"}, "web_widget_script": {"type": "string", "description": "<PERSON><PERSON>t used to load the website widget"}, "welcome_title": {"type": "string", "description": "Welcome title to be displayed on the widget"}, "welcome_tagline": {"type": "string", "description": "Welcome tagline to be displayed on the widget"}, "greeting_enabled": {"type": "boolean", "description": "The flag which shows whether greeting is enabled"}, "greeting_message": {"type": "string", "description": "A greeting message when the user starts the conversation"}, "channel_id": {"type": "number", "description": "ID of the channel this inbox belongs to"}, "working_hours_enabled": {"type": "boolean", "description": "The flag which shows whether working hours feature is enabled"}, "enable_email_collect": {"type": "boolean", "description": "The flag to enable collecting email from contacts"}, "csat_survey_enabled": {"type": "boolean", "description": "The flag to enable CSAT survey"}, "auto_assignment_config": {"type": "object", "description": "Configuration settings for auto assignment"}, "out_of_office_message": {"type": "string", "description": "Message to show when agents are out of office"}, "working_hours": {"type": "array", "description": "Configuration for working hours of the inbox", "items": {"type": "object", "properties": {"day_of_week": {"type": "number", "description": "Day of the week (0-6, where 0 is Sunday)"}, "closed_all_day": {"type": "boolean", "description": "Whether the inbox is closed for the entire day"}, "open_hour": {"type": "number", "description": "Hour when inbox opens (0-23)"}, "open_minutes": {"type": "number", "description": "Minutes of the hour when inbox opens (0-59)"}, "close_hour": {"type": "number", "description": "Hour when inbox closes (0-23)"}, "close_minutes": {"type": "number", "description": "Minutes of the hour when inbox closes (0-59)"}, "open_all_day": {"type": "boolean", "description": "Whether the inbox is open for the entire day"}}}}, "timezone": {"type": "string", "description": "Timezone configuration for the inbox"}, "callback_webhook_url": {"type": "string", "description": "Webhook URL for callbacks"}, "allow_messages_after_resolved": {"type": "boolean", "description": "Whether to allow messages after a conversation is resolved"}, "lock_to_single_conversation": {"type": "boolean", "description": "Whether to lock a contact to a single conversation"}, "sender_name_type": {"type": "string", "description": "Type of sender name to display (e.g., friendly)"}, "business_name": {"type": "string", "description": "Business name associated with the inbox"}, "hmac_mandatory": {"type": "boolean", "description": "Whether HMAC verification is mandatory"}, "selected_feature_flags": {"type": "object", "description": "Selected feature flags for the inbox"}, "reply_time": {"type": "string", "description": "Expected reply time"}, "messaging_service_sid": {"type": "string", "description": "Messaging service SID for SMS providers"}, "phone_number": {"type": "string", "description": "Phone number associated with the inbox"}, "medium": {"type": "string", "description": "Medium of communication (e.g., sms, email)"}, "provider": {"type": "string", "description": "Provider of the channel"}}}, "inbox_contact": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the inbox"}, "avatar_url": {"type": "string", "description": "The avatar image of the inbox"}, "channel_id": {"type": "number", "description": "The ID of the channel"}, "name": {"type": "string", "description": "The name of the inbox"}, "channel_type": {"type": "string", "description": "The type of the inbox"}, "provider": {"type": "string", "description": "The provider of the inbox"}}}, "agent_bot": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the agent bot"}, "name": {"type": "string", "description": "The name of the agent bot"}, "description": {"type": "string", "description": "The description about the agent bot"}, "thumbnail": {"type": "string", "description": "The thumbnail of the agent bot"}, "outgoing_url": {"type": "string", "description": "The webhook URL for the bot"}, "bot_type": {"type": "string", "description": "The type of the bot"}, "bot_config": {"type": "object", "description": "The configuration of the bot"}, "account_id": {"type": "number", "description": "Account ID if it's an account specific bot"}, "access_token": {"type": "string", "description": "The access token for the bot"}, "system_bot": {"type": "boolean", "description": "Whether the bot is a system bot"}}}, "contact_inboxes": {"type": "object", "properties": {"source_id": {"type": "string", "description": "Contact Inbox Source Id"}, "inbox": {"$ref": "#/components/schemas/inbox_contact"}}}, "contactable_inboxes": {"type": "object", "properties": {"source_id": {"type": "string", "description": "Contact Inbox Source Id"}, "inbox": {"$ref": "#/components/schemas/inbox"}}}, "custom_filter": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the custom filter"}, "name": {"type": "string", "description": "The name of the custom filter"}, "type": {"type": "string", "enum": ["conversation", "contact", "report"], "description": "The description about the custom filter"}, "query": {"type": "object", "description": "A query that needs to be saved as a custom filter"}, "created_at": {"type": "string", "format": "date-time", "description": "The time at which the custom filter was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "The time at which the custom filter was updated"}}}, "webhook": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the webhook"}, "url": {"type": "string", "description": "The url to which the events will be send"}, "subscriptions": {"type": "array", "items": {"type": "string", "enum": ["conversation_created", "conversation_status_changed", "conversation_updated", "contact_created", "contact_updated", "message_created", "message_updated", "webwidget_triggered"]}, "description": "The list of subscribed events"}, "account_id": {"type": "number", "description": "The id of the account which the webhook object belongs to"}}}, "account": {"type": "object", "properties": {"id": {"type": "number", "description": "Account ID"}, "name": {"type": "string", "description": "Name of the account"}, "role": {"type": "string", "enum": ["administrator", "agent"], "description": "The user role in the account"}}}, "account_detail": {"type": "object", "properties": {"id": {"type": "number", "description": "Account ID"}, "name": {"type": "string", "description": "Name of the account"}, "locale": {"type": "string", "description": "The locale of the account"}, "domain": {"type": "string", "description": "The domain of the account"}, "support_email": {"type": "string", "description": "The support email of the account"}, "status": {"type": "string", "description": "The status of the account"}, "created_at": {"type": "string", "format": "date-time", "description": "The creation date of the account"}, "cache_keys": {"type": "object", "description": "Cache keys for the account"}, "features": {"type": "array", "items": {"type": "string"}, "description": "Enabled features for the account"}, "settings": {"type": "object", "description": "Account settings", "properties": {"auto_resolve_after": {"type": "number", "description": "Auto resolve conversations after specified minutes"}, "auto_resolve_message": {"type": "string", "description": "Message to send when auto resolving"}, "auto_resolve_ignore_waiting": {"type": "boolean", "description": "Whether to ignore waiting conversations for auto resolve"}}}, "custom_attributes": {"type": "object", "description": "Custom attributes of the account", "properties": {"plan_name": {"type": "string", "description": "Subscription plan name"}, "subscribed_quantity": {"type": "number", "description": "Subscribed quantity"}, "subscription_status": {"type": "string", "description": "Subscription status"}, "subscription_ends_on": {"type": "string", "format": "date", "description": "Subscription end date"}, "industry": {"type": "string", "description": "Industry type"}, "company_size": {"type": "string", "description": "Company size"}, "timezone": {"type": "string", "description": "Account timezone"}, "logo": {"type": "string", "description": "Account logo URL"}, "onboarding_step": {"type": "string", "description": "Current onboarding step"}, "marked_for_deletion_at": {"type": "string", "format": "date-time", "description": "When account was marked for deletion"}, "marked_for_deletion_reason": {"type": "string", "description": "Reason for account deletion"}}}}}, "account_show_response": {"allOf": [{"$ref": "#/components/schemas/account_detail"}, {"type": "object", "properties": {"latest_chatwoot_version": {"type": "string", "description": "Latest version of Chatwoot available", "example": "3.0.0"}, "subscribed_features": {"type": "array", "items": {"type": "string"}, "description": "List of subscribed enterprise features (if enterprise edition is enabled)"}}}]}, "account_user": {"type": "array", "description": "Array of account users", "items": {"type": "object", "properties": {"account_id": {"type": "integer", "description": "The ID of the account"}, "user_id": {"type": "integer", "description": "The ID of the user"}, "role": {"type": "string", "description": "whether user is an administrator or agent"}}}}, "platform_account": {"type": "object", "properties": {"id": {"type": "number", "description": "Account ID"}, "name": {"type": "string", "description": "Name of the account"}}}, "team": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the team"}, "name": {"type": "string", "description": "The name of the team"}, "description": {"type": "string", "description": "The description about the team"}, "allow_auto_assign": {"type": "boolean", "description": "If this setting is turned on, the system would automatically assign the conversation to an agent in the team while assigning the conversation to a team"}, "account_id": {"type": "number", "description": "The ID of the account with the team is a part of"}, "is_member": {"type": "boolean", "description": "This field shows whether the current user is a part of the team"}}}, "integrations_app": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the integration"}, "name": {"type": "string", "description": "The name of the integration"}, "description": {"type": "string", "description": "The description about the team"}, "hook_type": {"type": "string", "description": "Whether the integration is an account or inbox integration"}, "enabled": {"type": "boolean", "description": "Whether the integration is enabled for the account"}, "allow_multiple_hooks": {"type": "boolean", "description": "Whether multiple hooks can be created for the integration"}, "hooks": {"type": "array", "items": {"type": "object"}, "description": "If there are any hooks created for this integration"}}}, "integrations_hook": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the integration hook"}, "app_id": {"type": "string", "description": "The ID of the integration app"}, "inbox_id": {"type": "string", "description": "Inbox ID if its an Inbox integration"}, "account_id": {"type": "string", "description": "Account ID of the integration"}, "status": {"type": "boolean", "description": "Whether the integration hook is enabled for the account"}, "hook_type": {"type": "boolean", "description": "Whether its an account or inbox integration hook"}, "settings": {"type": "object", "description": "The associated settings for the integration"}}}, "audit_log": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the audit log entry"}, "auditable_id": {"type": "integer", "description": "The ID of the audited object"}, "auditable_type": {"type": "string", "description": "The type of the audited object (e.g., Conversation, Contact, User)"}, "auditable": {"type": "object", "description": "The audited object data"}, "associated_id": {"type": "integer", "description": "The ID of the associated object (typically the account ID)"}, "associated_type": {"type": "string", "description": "The type of the associated object"}, "user_id": {"type": "integer", "description": "The ID of the user who performed the action"}, "user_type": {"type": "string", "description": "The type of user who performed the action"}, "username": {"type": "string", "description": "The email/username of the user who performed the action"}, "action": {"type": "string", "enum": ["create", "update", "destroy"], "description": "The action performed on the object"}, "audited_changes": {"type": "object", "description": "JSON object containing the changes made to the audited object"}, "version": {"type": "integer", "description": "Version number of the audit log entry"}, "comment": {"type": "string", "nullable": true, "description": "Optional comment associated with the audit log entry"}, "request_uuid": {"type": "string", "description": "UUID to identify the request that generated this audit log"}, "created_at": {"type": "integer", "description": "Unix timestamp when the audit log entry was created"}, "remote_address": {"type": "string", "nullable": true, "description": "IP address from which the action was performed"}}}, "public_contact": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id of the contact"}, "source_id": {"type": "string", "description": "The session identifier of the contact"}, "name": {"type": "string", "description": "Name of the contact"}, "email": {"type": "string", "description": "Email of the contact"}, "pubsub_token": {"type": "string", "description": "The token to be used to connect to chatwoot websocket"}}}, "public_conversation": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id of the conversation"}, "inbox_id": {"type": "string", "description": "The inbox id of the conversation"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/message"}, "description": "Messages in the conversation"}, "contact": {"type": "object", "description": "The contact information associated to the conversation"}}}, "public_message": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the message"}, "content": {"type": "string", "description": "Text content of the message"}, "message_type": {"type": "string", "description": "Denotes the message type"}, "content_type": {"type": "string", "description": "Content type of the message"}, "content_attributes": {"type": "string", "description": "Additional content attributes of the message"}, "created_at": {"type": "string", "description": "Created at time stamp of the message"}, "conversation_id": {"type": "string", "description": "Conversation Id of the message"}, "attachments": {"type": "array", "items": {"type": "object"}, "description": "Attachments if any"}, "sender": {"type": "object", "description": "Details of the sender"}}}, "public_inbox": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Inbox identifier"}, "name": {"type": "string", "description": "Name of the inbox"}, "timezone": {"type": "string", "description": "The timezone defined on the inbox"}, "working_hours": {"type": "array", "description": "The working hours defined on the inbox", "items": {"type": "object", "properties": {"day_of_week": {"type": "integer", "description": "Day of the week as a number. Sunday -> 0, Saturday -> 6"}, "open_all_day": {"type": "boolean", "description": "Whether or not the business is open the whole day"}, "closed_all_day": {"type": "boolean", "description": "Whether or not the business is closed the whole day"}, "open_hour": {"type": "integer", "description": "Opening hour. Can be null if closed all day"}, "open_minutes": {"type": "integer", "description": "Opening minute. Can be null if closed all day"}, "close_hour": {"type": "integer", "description": "Closing hour. Can be null if closed all day"}, "close_minutes": {"type": "integer", "description": "Closing minute. Can be null if closed all day"}}}}, "working_hours_enabled": {"type": "boolean", "description": "Whether of not the working hours are enabled on the inbox"}, "csat_survey_enabled": {"type": "boolean", "description": "Whether of not the Customer Satisfaction survey is enabled on the inbox"}, "greeting_enabled": {"type": "boolean", "description": "Whether of not the Greeting Message is enabled on the inbox"}, "identity_validation_enabled": {"type": "boolean", "description": "Whether of not the User Identity Validation is enforced on the inbox"}}}, "account_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the account", "example": "My Account"}, "locale": {"type": "string", "description": "The locale of the account", "example": "en"}, "domain": {"type": "string", "description": "The domain of the account", "example": "example.com"}, "support_email": {"type": "string", "description": "The support email of the account", "example": "<EMAIL>"}, "status": {"type": "string", "enum": ["active", "suspended"], "description": "The status of the account", "example": "active"}, "limits": {"type": "object", "description": "The limits of the account", "example": {}}, "custom_attributes": {"type": "object", "description": "The custom attributes of the account", "example": {}}}}, "account_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the account", "example": "My Account"}, "locale": {"type": "string", "description": "The locale of the account", "example": "en"}, "domain": {"type": "string", "description": "The domain of the account", "example": "example.com"}, "support_email": {"type": "string", "description": "The support email of the account", "example": "<EMAIL>"}, "auto_resolve_after": {"type": "integer", "minimum": 10, "maximum": 1439856, "nullable": true, "description": "Auto resolve conversations after specified minutes", "example": 1440}, "auto_resolve_message": {"type": "string", "nullable": true, "description": "Message to send when auto resolving", "example": "This conversation has been automatically resolved due to inactivity"}, "auto_resolve_ignore_waiting": {"type": "boolean", "nullable": true, "description": "Whether to ignore waiting conversations for auto resolve", "example": false}, "industry": {"type": "string", "description": "Industry type", "example": "Technology"}, "company_size": {"type": "string", "description": "Company size", "example": "50-100"}, "timezone": {"type": "string", "description": "Account timezone", "example": "UTC"}}}, "account_user_create_update_payload": {"type": "object", "required": ["user_id", "role"], "properties": {"user_id": {"type": "integer", "description": "The ID of the user", "example": 1}, "role": {"type": "string", "description": "whether user is an administrator or agent", "example": "administrator"}}}, "platform_agent_bot_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the agent bot", "example": "My Agent <PERSON>"}, "description": {"type": "string", "description": "The description of the agent bot", "example": "This is a sample agent bot"}, "outgoing_url": {"type": "string", "description": "The webhook URL for the bot", "example": "https://example.com/webhook"}, "account_id": {"type": "integer", "description": "The account ID to associate the agent bot with", "example": 1}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the agent bot avatar", "example": "https://example.com/avatar.png"}}}, "agent_bot_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the agent bot", "example": "My Agent <PERSON>"}, "description": {"type": "string", "description": "The description of the agent bot", "example": "This is a sample agent bot"}, "outgoing_url": {"type": "string", "description": "The webhook URL for the bot", "example": "https://example.com/webhook"}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the agent bot avatar", "example": "https://example.com/avatar.png"}, "bot_type": {"type": "integer", "description": "The type of the bot (0 for webhook)", "example": 0}, "bot_config": {"type": "object", "description": "The configuration for the bot", "example": {}}}}, "user_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the user", "example": "<PERSON>"}, "display_name": {"type": "string", "description": "Display name of the user", "example": "<PERSON>"}, "email": {"type": "string", "description": "Email of the user", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password must contain uppercase, lowercase letters, number and a special character", "example": "Password2!"}, "custom_attributes": {"type": "object", "description": "Custom attributes you want to associate with the user", "example": {}}}}, "canned_response_create_update_payload": {"type": "object", "properties": {"content": {"type": "string", "description": "Message content for canned response", "example": "Hello, {{contact.name}}! Welcome to our service."}, "short_code": {"type": "string", "description": "Short Code for quick access of the canned response", "example": "welcome"}}}, "custom_attribute_create_update_payload": {"type": "object", "properties": {"attribute_display_name": {"type": "string", "description": "Attribute display name", "example": "Custom Attribute"}, "attribute_display_type": {"type": "integer", "description": "Attribute display type (text- 0, number- 1, currency- 2, percent- 3, link- 4, date- 5, list- 6, checkbox- 7)", "example": 0}, "attribute_description": {"type": "string", "description": "Attribute description", "example": "This is a custom attribute"}, "attribute_key": {"type": "string", "description": "Attribute unique key value", "example": "custom_attribute"}, "attribute_values": {"type": "array", "description": "Attribute values", "items": {"type": "string"}, "example": ["value1", "value2"]}, "attribute_model": {"type": "integer", "description": "Attribute type(conversation_attribute- 0, contact_attribute- 1)", "example": 0}, "regex_pattern": {"type": "string", "description": "Regex pattern (Only applicable for type- text). The regex pattern is used to validate the attribute value(s).", "example": "^[a-zA-Z0-9]+$"}, "regex_cue": {"type": "string", "description": "Regex cue message (Only applicable for type- text). The cue message is shown when the regex pattern is not matched.", "example": "Please enter a valid value"}}}, "agent_create_payload": {"type": "object", "required": ["name", "email", "role"], "properties": {"name": {"type": "string", "description": "Full Name of the agent", "example": "<PERSON>"}, "email": {"type": "string", "description": "Email of the Agent", "example": "<EMAIL>"}, "role": {"type": "string", "enum": ["agent", "administrator"], "description": "Whether its administrator or agent", "example": "agent"}, "availability_status": {"type": "string", "enum": ["available", "busy", "offline"], "description": "The availability setting of the agent.", "example": "available"}, "auto_offline": {"type": "boolean", "description": "Whether the availability status of agent is configured to go offline automatically when away.", "example": true}}}, "agent_update_payload": {"type": "object", "required": ["role"], "properties": {"role": {"type": "string", "enum": ["agent", "administrator"], "description": "Whether its administrator or agent", "example": "agent"}, "availability_status": {"type": "string", "enum": ["available", "busy", "offline"], "description": "The availability status of the agent.", "example": "available"}, "auto_offline": {"type": "boolean", "description": "Whether the availability status of agent is configured to go offline automatically when away.", "example": true}}}, "contact_create_payload": {"type": "object", "required": ["inbox_id"], "properties": {"inbox_id": {"type": "number", "description": "ID of the inbox to which the contact belongs", "example": 1}, "name": {"type": "string", "description": "name of the contact", "example": "<PERSON>"}, "email": {"type": "string", "description": "email of the contact", "example": "<EMAIL>"}, "blocked": {"type": "boolean", "description": "whether the contact is blocked or not", "example": false}, "phone_number": {"type": "string", "description": "phone number of the contact", "example": "+123456789"}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the contact avatar", "example": "https://example.com/avatar.png"}, "identifier": {"type": "string", "description": "A unique identifier for the contact in external system", "example": "1234567890"}, "additional_attributes": {"type": "object", "description": "An object where you can store additional attributes for contact. example {\"type\":\"customer\", \"age\":30}", "example": {"type": "customer", "age": 30}}, "custom_attributes": {"type": "object", "description": "An object where you can store custom attributes for contact. example {\"type\":\"customer\", \"age\":30}, this should have a valid custom attribute definition.", "example": {}}}}, "contact_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "name of the contact", "example": "<PERSON>"}, "email": {"type": "string", "description": "email of the contact", "example": "<EMAIL>"}, "blocked": {"type": "boolean", "description": "whether the contact is blocked or not", "example": false}, "phone_number": {"type": "string", "description": "phone number of the contact", "example": "+123456789"}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the contact avatar", "example": "https://example.com/avatar.png"}, "identifier": {"type": "string", "description": "A unique identifier for the contact in external system", "example": "1234567890"}, "additional_attributes": {"type": "object", "description": "An object where you can store additional attributes for contact. example {\"type\":\"customer\", \"age\":30}", "example": {"type": "customer", "age": 30}}, "custom_attributes": {"type": "object", "description": "An object where you can store custom attributes for contact. example {\"type\":\"customer\", \"age\":30}, this should have a valid custom attribute definition.", "example": {}}}}, "conversation_create_payload": {"type": "object", "required": ["source_id", "inbox_id"], "properties": {"source_id": {"type": "string", "description": "Conversation source id", "example": "1234567890"}, "inbox_id": {"type": "integer", "description": "Id of inbox in which the conversation is created <br/> Allowed Inbox Types: Website, Phone, Api, Email", "example": 1}, "contact_id": {"type": "integer", "description": "Contact Id for which conversation is created", "example": 1}, "additional_attributes": {"type": "object", "description": "Lets you specify attributes like browser information", "example": {"browser": "Chrome", "browser_version": "89.0.4389.82", "os": "Windows", "os_version": "10"}}, "custom_attributes": {"type": "object", "description": "The object to save custom attributes for conversation, accepts custom attributes key and value", "example": {"attribute_key": "attribute_value", "priority_conversation_number": 3}}, "status": {"type": "string", "enum": ["open", "resolved", "pending"], "description": "Specify the conversation whether it's pending, open, closed", "example": "open"}, "assignee_id": {"type": "integer", "description": "Agent Id for assigning a conversation to an agent", "example": 1}, "team_id": {"type": "integer", "description": "Team Id for assigning a conversation to a team\\", "example": 1}, "snoozed_until": {"type": "string", "format": "date-time", "description": "Snoozed until date time", "example": "2030-07-21T17:32:28Z"}, "message": {"type": "object", "description": "The initial message to be sent to the conversation", "required": ["content"], "properties": {"content": {"type": "string", "description": "The content of the message", "example": "Hello, how can I help you?"}, "template_params": {"type": "object", "description": "The template params for the message in case of whatsapp Channel", "properties": {"name": {"type": "string", "description": "Name of the template", "example": "sample_issue_resolution"}, "category": {"type": "string", "description": "Category of the template", "example": "UTILITY"}, "language": {"type": "string", "description": "Language of the template", "example": "en_US"}, "processed_params": {"type": "object", "description": "The processed param values for template variables in template", "example": {"1": "Chatwoot"}}}}}}}}, "conversation_message_create_payload": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "description": "The content of the message", "example": "Hello, how can I help you?"}, "message_type": {"type": "string", "enum": ["outgoing", "incoming"], "description": "The type of the message", "example": "outgoing"}, "private": {"type": "boolean", "description": "Flag to identify if it is a private note", "example": false}, "content_type": {"type": "string", "enum": ["text", "input_email", "cards", "input_select", "form", "article"], "description": "Content type of the message", "example": "text"}, "content_attributes": {"type": "object", "description": "Attributes based on the content type", "example": {}}, "campaign_id": {"type": "integer", "description": "The campaign id to which the message belongs", "example": 1}, "template_params": {"type": "object", "description": "WhatsApp template parameters for sending structured messages", "required": ["name", "category", "language", "processed_params"], "properties": {"name": {"type": "string", "description": "Name of the WhatsApp template (must be approved in WhatsApp Business Manager)", "example": "purchase_receipt"}, "category": {"type": "string", "enum": ["UTILITY", "MARKETING", "SHIPPING_UPDATE", "TICKET_UPDATE", "ISSUE_RESOLUTION"], "description": "Category of the template", "example": "UTILITY"}, "language": {"type": "string", "description": "Language code of the template (BCP 47 format)", "example": "en_US"}, "processed_params": {"type": "object", "description": "Processed template parameters organized by component type", "properties": {"body": {"type": "object", "description": "Body component parameters with variable placeholders", "additionalProperties": {"type": "string"}, "example": {"1": "Visa", "2": "Nike", "3": "Bill"}}, "header": {"type": "object", "description": "Header component parameters for media templates", "properties": {"media_url": {"type": "string", "format": "uri", "description": "Publicly accessible URL for IMAGE, VIDEO, or DOCUMENT headers", "example": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}, "media_type": {"type": "string", "enum": ["image", "video", "document"], "description": "Type of media for the header", "example": "document"}}}, "buttons": {"type": "array", "description": "Button component parameters for interactive templates", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["url", "copy_code"], "description": "Type of button parameter"}, "parameter": {"type": "string", "description": "Dynamic parameter value for the button", "example": "SSFSDFSD"}}}}}}}}}}, "inbox_create_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the inbox", "example": "Support"}, "avatar": {"type": "string", "format": "binary", "description": "Image file for avatar"}, "greeting_enabled": {"type": "boolean", "description": "Enable greeting message", "example": true}, "greeting_message": {"type": "string", "description": "Greeting message to be displayed on the widget", "example": "Hello, how can I help you?"}, "enable_email_collect": {"type": "boolean", "description": "Enable email collection", "example": true}, "csat_survey_enabled": {"type": "boolean", "description": "Enable CSAT survey", "example": true}, "enable_auto_assignment": {"type": "boolean", "description": "Enable Auto Assignment", "example": true}, "working_hours_enabled": {"type": "boolean", "description": "Enable working hours", "example": true}, "out_of_office_message": {"type": "string", "description": "Out of office message to be displayed on the widget", "example": "We are currently out of office. Please leave a message and we will get back to you."}, "timezone": {"type": "string", "description": "Timezone of the inbox", "example": "America/New_York"}, "allow_messages_after_resolved": {"type": "boolean", "description": "Allow messages after conversation is resolved", "example": true}, "lock_to_single_conversation": {"type": "boolean", "description": "Lock to single conversation", "example": true}, "portal_id": {"type": "integer", "description": "Id of the help center portal to attach to the inbox", "example": 1}, "sender_name_type": {"type": "string", "description": "Sender name type for the inbox", "enum": ["friendly", "professional"], "example": "friendly"}, "business_name": {"type": "string", "description": "Business name for the inbox", "example": "My Business"}, "channel": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of the channel", "enum": ["web_widget", "api", "email", "line", "telegram", "whatsapp", "sms"], "example": "web_widget"}, "website_url": {"type": "string", "description": "URL at which the widget will be loaded", "example": "https://example.com"}, "welcome_title": {"type": "string", "description": "Welcome title to be displayed on the widget", "example": "Welcome to our support"}, "welcome_tagline": {"type": "string", "description": "Welcome tagline to be displayed on the widget", "example": "We are here to help you"}, "widget_color": {"type": "string", "description": "A Hex-color string used to customize the widget", "example": "#FF5733"}}}}}, "inbox_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the inbox", "example": "Support"}, "avatar": {"type": "string", "format": "binary", "description": "Image file for avatar"}, "greeting_enabled": {"type": "boolean", "description": "Enable greeting message", "example": true}, "greeting_message": {"type": "string", "description": "Greeting message to be displayed on the widget", "example": "Hello, how can I help you?"}, "enable_email_collect": {"type": "boolean", "description": "Enable email collection", "example": true}, "csat_survey_enabled": {"type": "boolean", "description": "Enable CSAT survey", "example": true}, "enable_auto_assignment": {"type": "boolean", "description": "Enable Auto Assignment", "example": true}, "working_hours_enabled": {"type": "boolean", "description": "Enable working hours", "example": true}, "out_of_office_message": {"type": "string", "description": "Out of office message to be displayed on the widget", "example": "We are currently out of office. Please leave a message and we will get back to you."}, "timezone": {"type": "string", "description": "Timezone of the inbox", "example": "America/New_York"}, "allow_messages_after_resolved": {"type": "boolean", "description": "Allow messages after conversation is resolved", "example": true}, "lock_to_single_conversation": {"type": "boolean", "description": "Lock to single conversation", "example": true}, "portal_id": {"type": "integer", "description": "Id of the help center portal to attach to the inbox", "example": 1}, "sender_name_type": {"type": "string", "description": "Sender name type for the inbox", "enum": ["friendly", "professional"], "example": "friendly"}, "business_name": {"type": "string", "description": "Business name for the inbox", "example": "My Business"}, "channel": {"type": "object", "properties": {"website_url": {"type": "string", "description": "URL at which the widget will be loaded", "example": "https://example.com"}, "welcome_title": {"type": "string", "description": "Welcome title to be displayed on the widget", "example": "Welcome to our support"}, "welcome_tagline": {"type": "string", "description": "Welcome tagline to be displayed on the widget", "example": "We are here to help you"}, "widget_color": {"type": "string", "description": "A Hex-color string used to customize the widget", "example": "#FF5733"}}}}}, "team_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the team", "example": "Support Team"}, "description": {"type": "string", "description": "The description of the team", "example": "This is a team of support agents"}, "allow_auto_assign": {"type": "boolean", "description": "If this setting is turned on, the system would automatically assign the conversation to an agent in the team while assigning the conversation to a team", "example": true}}}, "custom_filter_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the custom filter", "example": "My Custom Filter"}, "type": {"type": "string", "enum": ["conversation", "contact", "report"], "description": "The description about the custom filter", "example": "conversation"}, "query": {"type": "object", "description": "A query that needs to be saved as a custom filter", "example": {}}}}, "webhook_create_update_payload": {"type": "object", "properties": {"url": {"type": "string", "description": "The url where the events should be sent", "example": "https://example.com/webhook"}, "subscriptions": {"type": "array", "items": {"type": "string", "enum": ["conversation_created", "conversation_status_changed", "conversation_updated", "message_created", "message_updated", "contact_created", "contact_updated", "webwidget_triggered"]}, "description": "The events you want to subscribe to.", "example": ["conversation_created", "conversation_status_changed"]}}}, "integrations_hook_create_payload": {"type": "object", "properties": {"app_id": {"type": "integer", "description": "The ID of app for which integration hook is being created", "example": 1}, "inbox_id": {"type": "integer", "description": "The inbox ID, if the hook is an inbox hook", "example": 1}, "status": {"type": "integer", "description": "The status of the integration (0 for inactive, 1 for active)", "example": 1}, "settings": {"type": "object", "description": "The settings required by the integration", "example": {}}}}, "integrations_hook_update_payload": {"type": "object", "properties": {"status": {"type": "integer", "description": "The status of the integration (0 for inactive, 1 for active)", "example": 1}, "settings": {"type": "object", "description": "The settings required by the integration", "example": {}}}}, "automation_rule_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Rule name", "example": "Add label on message create event"}, "description": {"type": "string", "description": "The description about the automation and actions", "example": "Add label support and sales on message create event if incoming message content contains text help"}, "event_name": {"type": "string", "enum": ["conversation_created", "conversation_updated", "conversation_resolved", "message_created"], "example": "message_created", "description": "The event when you want to execute the automation actions"}, "active": {"type": "boolean", "description": "Enable/disable automation rule"}, "actions": {"type": "array", "description": "Array of actions which you want to perform when condition matches, e.g add label support if message contains content help.", "items": {"type": "object", "example": {"action_name": "add_label", "action_params": ["support"]}}}, "conditions": {"type": "array", "description": "Array of conditions on which conversation filter would work, e.g message content contains text help.", "items": {"type": "object", "example": {"attribute_key": "content", "filter_operator": "contains", "query_operator": "OR", "values": ["help"]}}}}}, "portal_create_update_payload": {"type": "object", "properties": {"color": {"type": "string", "description": "Header color for help-center in hex format", "example": "#FFFFFF"}, "custom_domain": {"type": "string", "description": "Custom domain to display help center.", "example": "chatwoot.help"}, "header_text": {"type": "string", "description": "Help center header", "example": "Handbook"}, "homepage_link": {"type": "string", "description": "link to main dashboard", "example": "https://www.chatwoot.com/"}, "name": {"type": "string", "description": "Name for the portal", "example": "Handbook"}, "page_title": {"type": "string", "description": "Page title for the portal", "example": "Handbook"}, "slug": {"type": "string", "description": "Slug for the portal to display in link", "example": "handbook"}, "archived": {"type": "boolean", "description": "Status to check if portal is live", "example": false}, "config": {"type": "object", "description": "Configuration about supporting locales", "example": {"allowed_locales": ["en", "es"], "default_locale": "en"}}}}, "category_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the category", "example": "Category Name"}, "description": {"type": "string", "description": "A description for the category", "example": "Category description"}, "position": {"type": "integer", "description": "Category position in the portal list to sort", "example": 1}, "slug": {"type": "string", "description": "The category slug used in the URL", "example": "category-name"}, "locale": {"type": "string", "description": "The locale of the category", "example": "en"}, "icon": {"type": "string", "description": "The icon of the category as a string (emoji)", "example": "📚"}, "parent_category_id": {"type": "integer", "description": "To define parent category, e.g product documentation has multiple level features in sales category or in engineering category.", "example": 1}, "associated_category_id": {"type": "integer", "description": "To associate similar categories to each other, e.g same category of product documentation in different languages", "example": 2}}}, "article_create_update_payload": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the article", "example": "Article Title"}, "slug": {"type": "string", "description": "The slug of the article", "example": "article-title"}, "position": {"type": "integer", "description": "article position in category", "example": 1}, "content": {"type": "string", "description": "The text content.", "example": "This is the content of the article"}, "description": {"type": "string", "description": "The description of the article", "example": "This is the description of the article"}, "category_id": {"type": "integer", "description": "The category id of the article", "example": 1}, "author_id": {"type": "integer", "description": "The author agent id of the article", "example": 1}, "associated_article_id": {"type": "integer", "description": "To associate similar articles to each other, e.g to provide the link for the reference.", "example": 2}, "status": {"type": "integer", "description": "The status of the article. 0 for draft, 1 for published, 2 for archived", "example": 1}, "locale": {"type": "string", "description": "The locale of the article", "example": "en"}, "meta": {"type": "object", "description": "Use for search", "example": {"tags": ["article_name"], "title": "article title", "description": "article description"}}}}, "public_contact_create_update_payload": {"type": "object", "properties": {"identifier": {"type": "string", "description": "External identifier of the contact", "example": "1234567890"}, "identifier_hash": {"type": "string", "description": "Identifier hash prepared for HMAC authentication", "example": "e93275d4eba0e5679ad55f5360af00444e2a888df9b0afa3e8b691c3173725f9"}, "email": {"type": "string", "description": "Email of the contact", "example": "<EMAIL>"}, "name": {"type": "string", "description": "Name of the contact", "example": "<PERSON>"}, "phone_number": {"type": "string", "description": "Phone number of the contact", "example": "+123456789"}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "custom_attributes": {"type": "object", "description": "Custom attributes of the customer", "example": {}}}}, "public_message_create_payload": {"type": "object", "properties": {"content": {"type": "string", "description": "Content for the message", "example": "Hello, how can I help you?"}, "echo_id": {"type": "string", "description": "Temporary identifier which will be passed back via websockets", "example": "1234567890"}}}, "public_message_update_payload": {"type": "object", "properties": {"submitted_values": {"type": "object", "description": "Replies to the Bot Message Types", "properties": {"name": {"type": "string", "description": "The name of the submiitted value", "example": "My Name"}, "title": {"type": "string", "description": "The title of the submitted value", "example": "My Title"}, "value": {"type": "string", "description": "The value of the submitted value", "example": "value"}, "csat_survey_response": {"type": "object", "description": "The CSAT survey response", "properties": {"feedback_message": {"type": "string", "description": "The feedback message of the CSAT survey response", "example": "Great service!"}, "rating": {"type": "integer", "description": "The rating of the CSAT survey response", "example": 5}}}}}}}, "public_conversation_create_payload": {"type": "object", "properties": {"custom_attributes": {"type": "object", "description": "Custom attributes of the conversation", "example": {}}}}, "extended_contact": {"allOf": [{"$ref": "#/components/schemas/contact"}, {"type": "object", "properties": {"id": {"type": "number", "description": "Id of the user"}, "availability_status": {"type": "string", "enum": ["online", "offline"], "description": "Availability status of the user"}}}]}, "contact_base": {"allOf": [{"$ref": "#/components/schemas/generic_id"}, {"$ref": "#/components/schemas/contact"}]}, "contact_list": {"type": "array", "description": "array of contacts", "items": {"allOf": [{"$ref": "#/components/schemas/contact"}]}}, "contact_conversations": {"type": "array", "description": "array of conversations", "items": {"allOf": [{"$ref": "#/components/schemas/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The additional attributes of the sender"}, "availability_status": {"type": "string", "description": "The availability status of the sender"}, "email": {"type": "string", "description": "The email of the sender"}, "id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "phone_number": {"type": "string", "description": "The phone number of the sender"}, "blocked": {"type": "boolean", "description": "Whether the sender is blocked"}, "identifier": {"type": "string", "description": "The identifier of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "custom_attributes": {"type": "object", "description": "The custom attributes of the sender"}, "last_activity_at": {"type": "number", "description": "The last activity at of the sender"}, "created_at": {"type": "number", "description": "The created at of the sender"}}}, "channel": {"type": "string", "description": "Channel Type"}, "assignee": {"$ref": "#/components/schemas/user"}, "hmac_verified": {"type": "boolean", "description": "Whether the hmac is verified"}}}}}, {"type": "object", "properties": {"display_id": {"type": "number"}}}]}}, "contact_labels": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of labels", "items": {"type": "string"}}}}, "conversation_list": {"type": "object", "properties": {"data": {"type": "object", "properties": {"meta": {"type": "object", "properties": {"mine_count": {"type": "number"}, "unassigned_count": {"type": "number"}, "assigned_count": {"type": "number"}, "all_count": {"type": "number"}}}, "payload": {"type": "array", "description": "array of conversations", "items": {"allOf": [{"$ref": "#/components/schemas/generic_id"}, {"$ref": "#/components/schemas/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The additional attributes of the sender"}, "availability_status": {"type": "string", "description": "The availability status of the sender"}, "email": {"type": "string", "description": "The email of the sender"}, "id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "phone_number": {"type": "string", "description": "The phone number of the sender"}, "blocked": {"type": "boolean", "description": "Whether the sender is blocked"}, "identifier": {"type": "string", "description": "The identifier of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "custom_attributes": {"type": "object", "description": "The custom attributes of the sender"}, "last_activity_at": {"type": "number", "description": "The last activity at of the sender"}, "created_at": {"type": "number", "description": "The created at of the sender"}}}, "channel": {"type": "string", "description": "Channel Type"}, "assignee": {"$ref": "#/components/schemas/user"}, "hmac_verified": {"type": "boolean", "description": "Whether the hmac is verified"}}}}}]}}}}}}, "conversation_show": {"type": "object", "allOf": [{"$ref": "#/components/schemas/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The additional attributes of the sender"}, "availability_status": {"type": "string", "description": "The availability status of the sender"}, "email": {"type": "string", "description": "The email of the sender"}, "id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "phone_number": {"type": "string", "description": "The phone number of the sender"}, "blocked": {"type": "boolean", "description": "Whether the sender is blocked"}, "identifier": {"type": "string", "description": "The identifier of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "custom_attributes": {"type": "object", "description": "The custom attributes of the sender"}, "last_activity_at": {"type": "number", "description": "The last activity at of the sender"}, "created_at": {"type": "number", "description": "The created at of the sender"}}}, "channel": {"type": "string", "description": "Channel Type"}, "assignee": {"$ref": "#/components/schemas/user"}, "hmac_verified": {"type": "boolean", "description": "Whether the hmac is verified"}}}}}]}, "conversation_status_toggle": {"type": "object", "properties": {"meta": {"type": "object"}, "payload": {"type": "object", "properties": {"success": {"type": "boolean"}, "current_status": {"type": "string", "enum": ["open", "resolved"]}, "conversation_id": {"type": "number"}}}}}, "conversation_labels": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of labels", "items": {"type": "string"}}}}, "account_summary": {"type": "object", "properties": {"avg_first_response_time": {"type": "string"}, "avg_resolution_time": {"type": "string"}, "conversations_count": {"type": "number"}, "incoming_messages_count": {"type": "number"}, "outgoing_messages_count": {"type": "number"}, "resolutions_count": {"type": "number"}, "previous": {"type": "object", "properties": {"avg_first_response_time": {"type": "string"}, "avg_resolution_time": {"type": "string"}, "conversations_count": {"type": "number"}, "incoming_messages_count": {"type": "number"}, "outgoing_messages_count": {"type": "number"}, "resolutions_count": {"type": "number"}}}}}, "agent_conversation_metrics": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "email": {"type": "string"}, "thumbnail": {"type": "string"}, "availability": {"type": "string"}, "metric": {"type": "object", "properties": {"open": {"type": "number"}, "unattended": {"type": "number"}}}}}, "contact_detail": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The object containing additional attributes related to the contact", "properties": {"city": {"type": "string", "description": "City of the contact"}, "country": {"type": "string", "description": "Country of the contact"}, "country_code": {"type": "string", "description": "Country code of the contact"}, "created_at_ip": {"type": "string", "description": "IP address when the contact was created"}}}, "custom_attributes": {"type": "object", "description": "The custom attributes of the contact"}, "email": {"type": "string", "description": "The email address of the contact"}, "id": {"type": "integer", "description": "The ID of the contact"}, "identifier": {"type": "string", "description": "The identifier of the contact", "nullable": true}, "name": {"type": "string", "description": "The name of the contact"}, "phone_number": {"type": "string", "description": "The phone number of the contact", "nullable": true}, "thumbnail": {"type": "string", "description": "The thumbnail of the contact"}, "blocked": {"type": "boolean", "description": "Whether the contact is blocked"}, "type": {"type": "string", "description": "The type of entity", "enum": ["contact"]}}}, "message_detailed": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the message"}, "content": {"type": "string", "description": "The text content of the message"}, "inbox_id": {"type": "number", "description": "The ID of the inbox"}, "conversation_id": {"type": "number", "description": "The ID of the conversation"}, "message_type": {"type": "integer", "enum": [0, 1, 2, 3], "description": "The type of the message (0: incoming, 1: outgoing, 2: activity, 3: template)"}, "content_type": {"type": "string", "enum": ["text", "input_select", "cards", "form", "input_csat"], "description": "The type of the message content"}, "status": {"type": "string", "enum": ["sent", "delivered", "read", "failed"], "description": "The status of the message"}, "content_attributes": {"type": "object", "description": "The content attributes for each content_type", "properties": {"in_reply_to": {"type": "string", "description": "ID of the message this is replying to", "nullable": true}}}, "created_at": {"type": "integer", "description": "The timestamp when message was created"}, "private": {"type": "boolean", "description": "The flag which shows whether the message is private or not"}, "source_id": {"type": "string", "description": "The source ID of the message", "nullable": true}, "sender": {"$ref": "#/components/schemas/contact_detail"}}}, "conversation_meta": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "string"}, "description": "Labels associated with the conversation"}, "additional_attributes": {"type": "object", "properties": {"browser": {"type": "object", "properties": {"device_name": {"type": "string", "description": "Name of the device"}, "browser_name": {"type": "string", "description": "Name of the browser"}, "platform_name": {"type": "string", "description": "Name of the platform"}, "browser_version": {"type": "string", "description": "Version of the browser"}, "platform_version": {"type": "string", "description": "Version of the platform"}}}, "referer": {"type": "string", "description": "Referrer URL"}, "initiated_at": {"type": "object", "properties": {"timestamp": {"type": "string", "description": "Timestamp when the conversation was initiated"}}}, "browser_language": {"type": "string", "description": "Browser language setting"}, "conversation_language": {"type": "string", "description": "Conversation language"}}, "description": "Additional attributes of the conversation"}, "contact": {"$ref": "#/components/schemas/contact_detail"}, "agent_last_seen_at": {"type": "string", "description": "Timestamp when the agent last saw the conversation", "nullable": true}, "assignee_last_seen_at": {"type": "string", "description": "Timestamp when the assignee last saw the conversation", "nullable": true}}}, "conversation_messages": {"type": "object", "properties": {"meta": {"$ref": "#/components/schemas/conversation_meta"}, "payload": {"type": "array", "items": {"$ref": "#/components/schemas/message_detailed"}, "description": "List of messages in the conversation"}}}, "contact_meta": {"type": "object", "properties": {"count": {"type": "integer", "description": "Total number of contacts"}, "current_page": {"type": "string", "description": "Current page number"}}}, "contact_inbox": {"type": "object", "properties": {"source_id": {"type": "string", "description": "Source identifier for the contact inbox"}, "inbox": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID of the inbox"}, "avatar_url": {"type": "string", "description": "URL for the inbox avatar"}, "channel_id": {"type": "integer", "description": "ID of the channel"}, "name": {"type": "string", "description": "Name of the inbox"}, "channel_type": {"type": "string", "description": "Type of channel"}, "provider": {"type": "string", "description": "Provider of the inbox", "nullable": true}}}}}, "contact_list_item": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The object containing additional attributes related to the contact", "properties": {"city": {"type": "string", "description": "City of the contact"}, "country": {"type": "string", "description": "Country of the contact"}, "country_code": {"type": "string", "description": "Country code of the contact"}, "created_at_ip": {"type": "string", "description": "IP address when the contact was created"}}}, "availability_status": {"type": "string", "description": "Availability status of the contact", "enum": ["online", "offline"]}, "email": {"type": "string", "description": "The email address of the contact", "nullable": true}, "id": {"type": "integer", "description": "The ID of the contact"}, "name": {"type": "string", "description": "The name of the contact"}, "phone_number": {"type": "string", "description": "The phone number of the contact", "nullable": true}, "blocked": {"type": "boolean", "description": "Whether the contact is blocked"}, "identifier": {"type": "string", "description": "The identifier of the contact", "nullable": true}, "thumbnail": {"type": "string", "description": "The thumbnail of the contact"}, "custom_attributes": {"type": "object", "description": "The custom attributes of the contact"}, "last_activity_at": {"type": "integer", "description": "Timestamp of last activity", "nullable": true}, "created_at": {"type": "integer", "description": "Timestamp when contact was created"}, "contact_inboxes": {"type": "array", "description": "List of inboxes associated with this contact", "items": {"$ref": "#/components/schemas/contact_inbox"}}}}, "contacts_list_response": {"type": "object", "properties": {"meta": {"$ref": "#/components/schemas/contact_meta"}, "payload": {"type": "array", "items": {"$ref": "#/components/schemas/contact_list_item"}, "description": "List of contacts"}}}, "contact_show_response": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/contact_list_item"}}}, "contact_conversation_message": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID of the message"}, "content": {"type": "string", "description": "Content of the message"}, "account_id": {"type": "integer", "description": "ID of the account"}, "inbox_id": {"type": "integer", "description": "ID of the inbox"}, "conversation_id": {"type": "integer", "description": "ID of the conversation"}, "message_type": {"type": "integer", "description": "Type of the message"}, "created_at": {"type": "integer", "description": "Timestamp when message was created"}, "updated_at": {"type": "string", "description": "Formatted datetime when message was updated"}, "private": {"type": "boolean", "description": "Whether the message is private"}, "status": {"type": "string", "description": "Status of the message"}, "source_id": {"type": "string", "description": "Source ID of the message", "nullable": true}, "content_type": {"type": "string", "description": "Type of the content"}, "content_attributes": {"type": "object", "description": "Attributes of the content"}, "sender_type": {"type": "string", "description": "Type of the sender", "nullable": true}, "sender_id": {"type": "integer", "description": "ID of the sender", "nullable": true}, "external_source_ids": {"type": "object", "description": "External source IDs"}, "additional_attributes": {"type": "object", "description": "Additional attributes of the message"}, "processed_message_content": {"type": "string", "description": "Processed message content", "nullable": true}, "sentiment": {"type": "object", "description": "Sentiment analysis of the message"}, "conversation": {"type": "object", "description": "Conversation details", "properties": {"assignee_id": {"type": "integer", "description": "ID of the assignee", "nullable": true}, "unread_count": {"type": "integer", "description": "Count of unread messages"}, "last_activity_at": {"type": "integer", "description": "Timestamp of last activity"}, "contact_inbox": {"type": "object", "description": "Contact inbox details", "properties": {"source_id": {"type": "string", "description": "Source ID of the contact inbox"}}}}}, "sender": {"type": "object", "description": "Details of the sender", "properties": {"id": {"type": "integer", "description": "ID of the sender"}, "name": {"type": "string", "description": "Name of the sender"}, "available_name": {"type": "string", "description": "Available name of the sender"}, "avatar_url": {"type": "string", "description": "URL of the sender's avatar"}, "type": {"type": "string", "description": "Type of the sender"}, "availability_status": {"type": "string", "description": "Availability status of the sender"}, "thumbnail": {"type": "string", "description": "Thumbnail URL of the sender"}}}}}, "contact_conversations_response": {"type": "object", "properties": {"payload": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"additional_attributes": {"type": "object", "description": "The additional attributes of the sender"}, "availability_status": {"type": "string", "description": "The availability status of the sender"}, "email": {"type": "string", "description": "The email of the sender"}, "id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "phone_number": {"type": "string", "description": "The phone number of the sender"}, "blocked": {"type": "boolean", "description": "Whether the sender is blocked"}, "identifier": {"type": "string", "description": "The identifier of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "custom_attributes": {"type": "object", "description": "The custom attributes of the sender"}, "last_activity_at": {"type": "number", "description": "The last activity at of the sender"}, "created_at": {"type": "number", "description": "The created at of the sender"}}}, "channel": {"type": "string", "description": "Channel Type"}, "assignee": {"$ref": "#/components/schemas/user"}, "hmac_verified": {"type": "boolean", "description": "Whether the hmac is verified"}}}}}]}, "description": "List of conversations for the contact"}}}, "contactable_inboxes_response": {"type": "object", "properties": {"payload": {"type": "array", "items": {"$ref": "#/components/schemas/contact_inbox"}, "description": "List of contactable inboxes for the contact"}}}}, "parameters": {"account_id": {"in": "path", "name": "account_id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the account"}, "agent_bot_id": {"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the agentbot to be updated"}, "team_id": {"in": "path", "name": "team_id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the team to be updated"}, "inbox_id": {"in": "path", "name": "inbox_id", "schema": {"type": "integer"}, "required": true, "description": "The ID of the Inbox"}, "hook_id": {"in": "path", "name": "hook_id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the integration hook"}, "source_id": {"in": "path", "name": "source_id", "required": true, "schema": {"type": "string"}, "description": "Id of the session for which the conversation is created.\n\n\n\n Source Ids can be obtained through contactable inboxes API or via generated.<br/><br/>Website: Chatwoot generated string which can be obtained from webhook events. <br/> Phone Channels(Twilio): Phone number in e164 format <br/> Email Channels: Contact Email address <br/> API Channel: Any Random String"}, "contact_sort_param": {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["name", "email", "phone_number", "last_activity_at", "-name", "-email", "-phone_number", "-last_activity_at"]}, "required": false, "description": "The attribute by which list should be sorted"}, "conversation_id": {"in": "path", "name": "conversation_id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the conversation"}, "conversation_uuid": {"in": "path", "name": "conversation_uuid", "schema": {"type": "integer"}, "required": true, "description": "The uuid of the conversation"}, "custom_filter_id": {"in": "path", "name": "custom_filter_id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the custom filter"}, "webhook_id": {"in": "path", "name": "webhook_id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the webhook"}, "message_id": {"in": "path", "name": "message_id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the message"}, "page": {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "required": false, "description": "The page parameter"}, "platform_user_id": {"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "The numeric ID of the user on the platform"}, "report_type": {"in": "query", "name": "type", "schema": {"type": "string", "enum": ["account", "agent", "inbox", "label", "team"]}, "required": true, "description": "Type of report"}, "report_metric": {"in": "query", "name": "metric", "schema": {"type": "string", "enum": ["conversations_count", "incoming_messages_count", "outgoing_messages_count", "avg_first_response_time", "avg_resolution_time", "resolutions_count"]}, "required": true, "description": "The type of metric"}, "public_inbox_identifier": {"in": "path", "name": "inbox_identifier", "schema": {"type": "string"}, "required": true, "description": "The identifier obtained from API inbox channel"}, "public_contact_identifier": {"in": "path", "name": "contact_identifier", "schema": {"type": "string"}, "required": true, "description": "The source id of contact obtained on contact create"}, "portal_id": {"in": "path", "name": "id", "schema": {"type": "string"}, "required": true, "description": "The slug identifier of the portal"}}, "securitySchemes": {"userApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api_access_token", "description": "This token can be obtained by visiting the profile page or via rails console. Provides access to  endpoints based on the user permissions levels. This token can be saved by an external system when user is created via API, to perform activities on behalf of the user."}, "agentBotApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api_access_token", "description": "This token should be provided by system admin or obtained via rails console. This token can be used to build bot integrations and can only access limited apis."}, "platformAppApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api_access_token", "description": "This token can be obtained by the system admin after creating a platformApp. This token should be used to provision agent bots, accounts, users and their roles."}}}, "tags": [{"name": "Accounts", "description": "Account management APIs"}, {"name": "Account Users", "description": "Account user management APIs"}, {"name": "AgentBots", "description": "Bot integrations"}, {"name": "Users", "description": "User management APIs"}, {"name": "Account AgentBots", "description": "Account-specific Agent <PERSON>"}, {"name": "Agents", "description": "Agent management APIs"}, {"name": "Canned Responses", "description": "Pre-defined responses for common queries"}, {"name": "Contacts", "description": "Contact management APIs"}, {"name": "Contact Labels", "description": "Manage contact labels"}, {"name": "Conversation Assignments", "description": "Manage conversation assignments"}, {"name": "Conversation Labels", "description": "Manage conversation labels"}, {"name": "Conversations", "description": "Conversation management APIs"}, {"name": "Custom Attributes", "description": "Custom fields for contacts and conversations"}, {"name": "Custom Filters", "description": "Saved filters for conversations"}, {"name": "Inboxes", "description": "Communication channels setup"}, {"name": "Integrations", "description": "Third-party integrations"}, {"name": "Messages", "description": "Message management APIs"}, {"name": "Profile", "description": "User profile APIs"}, {"name": "Reports", "description": "Analytics and reporting APIs"}, {"name": "Teams", "description": "Team management APIs"}, {"name": "Webhooks", "description": "Event notification webhooks"}, {"name": "Automation Rule", "description": "Workflow automation rules"}, {"name": "Help Center", "description": "Knowledge base management"}, {"name": "Contacts API", "description": "Public contact APIs"}, {"name": "Conversations API", "description": "Public conversation APIs"}, {"name": "Messages API", "description": "Public message APIs"}, {"name": "CSAT Survey Page", "description": "Customer satisfaction survey"}], "x-tagGroups": [{"name": "Platform", "tags": ["Accounts", "Account Users", "AgentBots", "Users"]}, {"name": "Application", "tags": ["Account AgentBots", "Account", "Agents", "<PERSON><PERSON>", "Canned Responses", "Contacts", "Contact Labels", "Conversation Assignments", "Conversation Labels", "Conversations", "Custom Attributes", "Custom Filters", "Inboxes", "Integrations", "Messages", "Profile", "Reports", "Teams", "Webhooks", "Automation Rule", "Help Center"]}, {"name": "Client", "tags": ["Contacts API", "Conversations API", "Messages API"]}, {"name": "Others", "tags": ["CSAT Survey Page"]}]}