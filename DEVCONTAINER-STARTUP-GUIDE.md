# 🚀 VS Code Dev Container 启动指南

本指南将帮助您使用VS Code Dev Container功能启动Chatwoot开发环境。

## 📋 前提条件检查

在开始之前，请确保：

- ✅ **VS Code** 已安装最新版本
- ✅ **Dev Containers扩展** 已安装 (`ms-vscode-remote.remote-containers`)
- ✅ **Docker Desktop** 正在运行
- ✅ 项目已在VS Code中打开

## 🎯 第一步：启动Dev Container

### 方法一：通过通知启动
1. 在VS Code中打开项目
2. VS Code会自动检测到`.devcontainer`配置
3. 点击右下角弹出的通知："**Reopen in Container**"

### 方法二：通过命令面板启动
1. 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
2. 输入：`Dev Containers: Reopen in Container`
3. 选择该命令

### 方法三：通过状态栏启动
1. 点击VS Code左下角的绿色图标 `><`
2. 选择 "**Reopen in Container**"

## ⏳ 第二步：等待容器构建

首次启动时，VS Code会：

1. **下载基础镜像** (约2-3分钟)
2. **构建开发镜像** (约3-5分钟)
3. **启动所有服务** (约1-2分钟)
4. **安装VS Code扩展** (约1分钟)
5. **运行初始化脚本** (约2-3分钟)

总计约需要 **10-15分钟**（仅首次）

### 构建过程中您会看到：
```
[+] Building 45.2s (23/23) FINISHED
[+] Running 8/8
 ✔ Network chatwoot-network     Created
 ✔ Container chatwoot-postgres-dev  Started
 ✔ Container chatwoot-redis-dev     Started
 ✔ Container chatwoot-mailhog-dev   Started
 ✔ Container chatwoot-rails-dev     Started
```

## 🔍 第三步：验证环境配置

容器启动后，在VS Code集成终端中运行检查脚本：

```bash
# 运行环境检查
./scripts/check-devcontainer.sh
```

### 预期输出：
```
🔍 检查 Chatwoot Dev Container 环境...
[✅ SUCCESS] 项目目录正确
[✅ SUCCESS] 环境文件存在
[✅ SUCCESS] PostgreSQL 连接正常
[✅ SUCCESS] Redis 连接正常
[✅ SUCCESS] Ruby依赖已安装
[✅ SUCCESS] Node.js依赖已安装
[✅ SUCCESS] 数据库已初始化
```

## 🚀 第四步：初始化开发环境

运行初始化脚本：

```bash
# 初始化开发环境
./scripts/start-devcontainer.sh
```

### 脚本会自动：
1. ✅ 等待所有服务启动
2. ✅ 安装Ruby和Node.js依赖
3. ✅ 创建并初始化数据库
4. ✅ 运行数据库迁移和种子数据
5. ✅ 清理临时文件
6. ✅ 准备开发环境

## 🎮 第五步：启动开发服务器

### 5.1 启动Rails服务器
在VS Code集成终端中：

```bash
# 启动Rails服务器
bundle exec rails server

# 或使用快捷脚本
./scripts/start-devcontainer.sh rails
```

### 5.2 启动Vite开发服务器
打开新的集成终端 (`Ctrl+Shift+\``)：

```bash
# 启动Vite开发服务器
bin/vite dev

# 或使用快捷脚本
./scripts/start-devcontainer.sh vite
```

### 5.3 启动Sidekiq（可选）
再打开一个新终端：

```bash
# 启动后台任务处理
bundle exec sidekiq

# 或使用快捷脚本
./scripts/start-devcontainer.sh sidekiq
```

## 🌐 第六步：验证应用访问

### 主要服务
- **🌐 Rails主应用**: http://localhost:3000
- **⚡ Vite开发服务器**: http://localhost:3036

### 开发工具
- **📧 MailHog邮件测试**: http://localhost:8025
- **🗄️ Adminer数据库管理**: http://localhost:8080
- **🔴 Redis Commander**: http://localhost:8081

### 验证步骤：
1. 访问 http://localhost:3000
2. 应该看到Chatwoot登录页面
3. 使用种子数据账户登录：
   - 邮箱: `<EMAIL>`
   - 密码: `chatwoot123`

## 🛠️ 日常开发命令

### Rails相关
```bash
# Rails控制台
bundle exec rails console

# 数据库迁移
bundle exec rails db:migrate

# 运行测试
bundle exec rspec

# 代码检查
bundle exec rubocop
```

### 前端相关
```bash
# 安装新的npm包
pnpm add package-name

# 运行前端测试
pnpm test

# 代码格式化
pnpm run eslint:fix
```

### 数据库操作
```bash
# 重置数据库
bundle exec rails db:reset

# 创建新迁移
bundle exec rails generate migration MigrationName

# 查看数据库状态
bundle exec rails db:migrate:status
```

## 🔧 VS Code集成功能

### 自动安装的扩展
- **Ruby LSP**: 智能代码补全和错误检查
- **RuboCop**: 自动代码格式化
- **Vue.js**: 前端开发支持
- **GitHub Copilot**: AI代码助手

### 调试功能
1. **Ruby调试**: 在代码中添加 `binding.pry`
2. **前端调试**: 使用浏览器开发者工具
3. **断点调试**: 使用VS Code内置调试器

### 快捷键
- `Ctrl+Shift+\``: 打开新的集成终端
- `Cmd+Shift+P`: 命令面板
- `F5`: 启动调试
- `Ctrl+\``: 显示/隐藏终端

## ❓ 常见问题解决

### Q: 容器启动失败？
```bash
# 检查Docker状态
docker ps

# 重新构建容器
# 在VS Code中: Cmd+Shift+P → "Dev Containers: Rebuild Container"
```

### Q: 端口访问不了？
```bash
# 检查服务状态
./scripts/check-devcontainer.sh

# 检查端口转发
netstat -tuln | grep :3000
```

### Q: 数据库连接失败？
```bash
# 检查PostgreSQL状态
pg_isready -h postgres -p 5432 -U postgres

# 重新初始化数据库
bundle exec rails db:drop db:create db:migrate db:seed
```

### Q: 依赖安装失败？
```bash
# 重新安装Ruby依赖
bundle install

# 重新安装Node.js依赖
pnpm install
```

## 🎯 性能优化建议

### Docker资源配置
在Docker Desktop中调整：
- **CPU**: 4-8核心
- **内存**: 8-16GB
- **磁盘**: 使用SSD

### VS Code设置
```json
{
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/tmp/**": true,
    "**/log/**": true
  }
}
```

## 🔄 重启和清理

### 重启容器
```bash
# 在VS Code中
# Cmd+Shift+P → "Dev Containers: Rebuild Container"
```

### 清理资源
```bash
# 清理Docker资源
docker system prune -f

# 清理项目缓存
rm -rf tmp/cache/*
rm -rf node_modules/.cache
```

## 🎉 开始开发！

现在您的Chatwoot Dev Container环境已经完全配置好了！

### 下一步：
1. 🔍 浏览代码结构
2. 🛠️ 开始修改代码
3. 🧪 运行测试
4. 📝 查看文档

### 有用的资源：
- **开发文档**: [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)
- **VS Code配置**: [docs/VSCODE-DEVCONTAINER.md](docs/VSCODE-DEVCONTAINER.md)
- **API文档**: http://localhost:3000/swagger

---

**🎊 祝您开发愉快！** 如果遇到问题，请查看故障排除部分或参考详细文档。
