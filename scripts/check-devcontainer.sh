#!/bin/bash

# Dev Container 环境检查脚本
# 用于验证开发环境是否正确配置

set -e

echo "🔍 检查 Chatwoot Dev Container 环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ ERROR]${NC} $1"
}

# 检查基本环境
check_basic_environment() {
    log_info "检查基本环境..."
    
    # 检查当前目录
    if [ -f "Gemfile" ] && [ -f "package.json" ]; then
        log_success "项目目录正确"
    else
        log_error "项目目录不正确，请确保在Chatwoot根目录"
        exit 1
    fi
    
    # 检查环境文件
    if [ -f ".env" ]; then
        log_success "环境文件存在"
    else
        log_warning "环境文件不存在，正在创建..."
        cp .env.development .env
        log_success "环境文件已创建"
    fi
}

# 检查服务连接
check_services() {
    log_info "检查服务连接..."
    
    # 检查PostgreSQL
    if pg_isready -h postgres -p 5432 -U postgres > /dev/null 2>&1; then
        log_success "PostgreSQL 连接正常"
    else
        log_error "PostgreSQL 连接失败"
        return 1
    fi
    
    # 检查Redis
    if redis-cli -h redis -p 6379 ping > /dev/null 2>&1; then
        log_success "Redis 连接正常"
    else
        log_error "Redis 连接失败"
        return 1
    fi
    
    # 检查MailHog
    if curl -s http://mailhog:8025 > /dev/null 2>&1; then
        log_success "MailHog 服务正常"
    else
        log_warning "MailHog 服务可能未启动"
    fi
}

# 检查Ruby环境
check_ruby_environment() {
    log_info "检查Ruby环境..."
    
    # 检查Ruby版本
    ruby_version=$(ruby -v)
    log_success "Ruby版本: $ruby_version"
    
    # 检查Bundler
    if command -v bundle > /dev/null 2>&1; then
        bundler_version=$(bundle -v)
        log_success "Bundler版本: $bundler_version"
    else
        log_error "Bundler未安装"
        return 1
    fi
    
    # 检查Gem依赖
    if bundle check > /dev/null 2>&1; then
        log_success "Ruby依赖已安装"
    else
        log_warning "Ruby依赖需要安装"
        log_info "运行: bundle install"
    fi
}

# 检查Node.js环境
check_node_environment() {
    log_info "检查Node.js环境..."
    
    # 检查Node.js版本
    if command -v node > /dev/null 2>&1; then
        node_version=$(node -v)
        log_success "Node.js版本: $node_version"
    else
        log_error "Node.js未安装"
        return 1
    fi
    
    # 检查pnpm
    if command -v pnpm > /dev/null 2>&1; then
        pnpm_version=$(pnpm -v)
        log_success "pnpm版本: $pnpm_version"
    else
        log_error "pnpm未安装"
        return 1
    fi
    
    # 检查Node.js依赖
    if [ -d "node_modules" ]; then
        log_success "Node.js依赖已安装"
    else
        log_warning "Node.js依赖需要安装"
        log_info "运行: pnpm install"
    fi
}

# 检查数据库
check_database() {
    log_info "检查数据库..."
    
    # 检查数据库连接
    if bundle exec rails runner "ActiveRecord::Base.connection" > /dev/null 2>&1; then
        log_success "数据库连接正常"
        
        # 检查数据库是否已初始化
        if bundle exec rails runner "User.count" > /dev/null 2>&1; then
            log_success "数据库已初始化"
        else
            log_warning "数据库需要初始化"
            log_info "运行: bundle exec rails db:setup"
        fi
    else
        log_warning "数据库连接失败或未初始化"
        log_info "运行: bundle exec rails db:create db:migrate db:seed"
    fi
}

# 检查端口
check_ports() {
    log_info "检查端口转发..."
    
    ports=(3000 3036 5432 6379 8025 8080 8081)
    port_names=("Rails" "Vite" "PostgreSQL" "Redis" "MailHog" "Adminer" "Redis Commander")
    
    for i in "${!ports[@]}"; do
        port=${ports[$i]}
        name=${port_names[$i]}
        
        if netstat -tuln | grep ":$port " > /dev/null 2>&1; then
            log_success "$name (端口 $port) 已绑定"
        else
            log_warning "$name (端口 $port) 未绑定"
        fi
    done
}

# 检查VS Code扩展
check_vscode_extensions() {
    log_info "检查VS Code扩展..."
    
    # 这里只是提示，因为在容器内无法直接检查VS Code扩展
    log_info "请确认以下扩展已安装："
    echo "  - Shopify.ruby-lsp (Ruby语言服务器)"
    echo "  - misogi.ruby-rubocop (Ruby代码格式化)"
    echo "  - Vue.volar (Vue.js支持)"
    echo "  - GitHub.copilot (AI代码助手)"
    echo "  - bradlc.vscode-tailwindcss (Tailwind CSS)"
}

# 显示访问地址
show_access_urls() {
    log_info "服务访问地址："
    echo ""
    echo "🌐 主要服务："
    echo "  Rails 主应用:        http://localhost:3000"
    echo "  Vite 开发服务器:      http://localhost:3036"
    echo ""
    echo "🛠️ 开发工具："
    echo "  MailHog 邮件测试:     http://localhost:8025"
    echo "  Adminer 数据库管理:   http://localhost:8080"
    echo "  Redis Commander:     http://localhost:8081"
    echo ""
    echo "🔌 数据库连接："
    echo "  PostgreSQL:          localhost:5432"
    echo "  Redis:               localhost:6379"
}

# 显示常用命令
show_common_commands() {
    log_info "常用开发命令："
    echo ""
    echo "📱 启动服务："
    echo "  bundle exec rails server    # 启动Rails服务器"
    echo "  bin/vite dev                # 启动Vite开发服务器"
    echo "  bundle exec sidekiq         # 启动后台任务处理"
    echo ""
    echo "🗄️ 数据库操作："
    echo "  bundle exec rails db:migrate    # 运行数据库迁移"
    echo "  bundle exec rails db:seed       # 运行种子数据"
    echo "  bundle exec rails db:reset      # 重置数据库"
    echo ""
    echo "🧪 开发工具："
    echo "  bundle exec rails console      # Rails控制台"
    echo "  bundle exec rspec              # 运行测试"
    echo "  bundle exec rubocop            # 代码检查"
}

# 主函数
main() {
    echo "🚀 Chatwoot Dev Container 环境检查"
    echo "=================================="
    echo ""
    
    check_basic_environment
    echo ""
    
    check_services
    echo ""
    
    check_ruby_environment
    echo ""
    
    check_node_environment
    echo ""
    
    check_database
    echo ""
    
    check_ports
    echo ""
    
    check_vscode_extensions
    echo ""
    
    show_access_urls
    echo ""
    
    show_common_commands
    echo ""
    
    log_success "🎉 环境检查完成！"
    echo ""
    echo "💡 提示："
    echo "  - 如果发现问题，请参考 docs/VSCODE-DEVCONTAINER.md"
    echo "  - 使用 Ctrl+Shift+\` 打开新的集成终端"
    echo "  - 修改代码后服务会自动重载"
}

# 执行主函数
main "$@"
