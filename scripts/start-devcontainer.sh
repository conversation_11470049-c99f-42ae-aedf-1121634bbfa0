#!/bin/bash

# Dev Container 开发环境启动脚本
# 用于在容器内初始化和启动开发服务

set -e

echo "🚀 启动 Chatwoot Dev Container 开发环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ ERROR]${NC} $1"
}

# 检查是否在容器内
check_container_environment() {
    if [ ! -f "/.dockerenv" ]; then
        log_error "此脚本需要在Dev Container内运行"
        log_info "请在VS Code中使用 'Reopen in Container' 功能"
        exit 1
    fi
    log_success "在Dev Container环境中"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待PostgreSQL
    log_info "等待PostgreSQL启动..."
    until pg_isready -h postgres -p 5432 -U postgres > /dev/null 2>&1; do
        echo "等待PostgreSQL..."
        sleep 2
    done
    log_success "PostgreSQL已就绪"
    
    # 等待Redis
    log_info "等待Redis启动..."
    until redis-cli -h redis -p 6379 ping > /dev/null 2>&1; do
        echo "等待Redis..."
        sleep 2
    done
    log_success "Redis已就绪"
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    # 安装Ruby依赖
    if ! bundle check > /dev/null 2>&1; then
        log_info "安装Ruby依赖..."
        bundle install
        log_success "Ruby依赖安装完成"
    else
        log_success "Ruby依赖已是最新"
    fi
    
    # 安装Node.js依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "安装Node.js依赖..."
        pnpm install
        log_success "Node.js依赖安装完成"
    else
        log_success "Node.js依赖已是最新"
    fi
}

# 初始化数据库
setup_database() {
    log_info "初始化数据库..."
    
    # 检查数据库是否存在
    if bundle exec rails runner "ActiveRecord::Base.connection" > /dev/null 2>&1; then
        log_info "数据库已存在，检查迁移状态..."
        
        # 运行待处理的迁移
        if bundle exec rails db:migrate:status | grep -q "down"; then
            log_info "运行数据库迁移..."
            bundle exec rails db:migrate
            log_success "数据库迁移完成"
        else
            log_success "数据库迁移已是最新"
        fi
        
        # 检查是否有数据
        user_count=$(bundle exec rails runner "puts User.count" 2>/dev/null || echo "0")
        if [ "$user_count" -eq "0" ]; then
            log_info "运行种子数据..."
            bundle exec rails db:seed
            log_success "种子数据创建完成"
        else
            log_success "数据库已有数据 ($user_count 个用户)"
        fi
    else
        log_info "创建并初始化数据库..."
        bundle exec rails db:create
        bundle exec rails db:migrate
        bundle exec rails db:seed
        log_success "数据库初始化完成"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理PID文件
    rm -rf tmp/pids/server.pid
    
    # 清理缓存
    rm -rf tmp/cache/*
    
    # 创建必要的目录
    mkdir -p tmp/pids
    mkdir -p log
    mkdir -p storage
    
    log_success "临时文件清理完成"
}

# 预编译资源（可选）
precompile_assets() {
    if [ "${PRECOMPILE_ASSETS:-false}" = "true" ]; then
        log_info "预编译开发资源..."
        bundle exec rails assets:precompile RAILS_ENV=development
        log_success "资源预编译完成"
    fi
}

# 启动开发服务器
start_development_servers() {
    log_info "准备启动开发服务器..."
    
    echo ""
    log_success "🎉 开发环境初始化完成！"
    echo ""
    echo "📱 现在可以启动开发服务器："
    echo ""
    echo "🚂 启动Rails服务器："
    echo "  bundle exec rails server"
    echo ""
    echo "⚡ 启动Vite开发服务器（新终端）："
    echo "  bin/vite dev"
    echo ""
    echo "⚙️ 启动Sidekiq后台任务（可选）："
    echo "  bundle exec sidekiq"
    echo ""
    echo "🌐 服务访问地址："
    echo "  主应用:           http://localhost:3000"
    echo "  Vite开发服务器:    http://localhost:3036"
    echo "  MailHog邮件测试:   http://localhost:8025"
    echo "  Adminer数据库:     http://localhost:8080"
    echo "  Redis Commander:  http://localhost:8081"
    echo ""
    echo "🛠️ 常用命令："
    echo "  bundle exec rails console    # Rails控制台"
    echo "  bundle exec rspec            # 运行测试"
    echo "  bundle exec rails db:migrate # 数据库迁移"
    echo ""
    echo "💡 提示："
    echo "  - 使用 Ctrl+Shift+\` 打开新的集成终端"
    echo "  - 代码修改会自动重载"
    echo "  - 查看日志: tail -f log/development.log"
}

# 显示环境信息
show_environment_info() {
    log_info "环境信息："
    echo "  Ruby版本: $(ruby -v)"
    echo "  Node.js版本: $(node -v)"
    echo "  pnpm版本: $(pnpm -v)"
    echo "  Rails版本: $(bundle exec rails -v)"
    echo "  工作目录: $(pwd)"
}

# 主函数
main() {
    echo "🚀 Chatwoot Dev Container 启动脚本"
    echo "===================================="
    echo ""
    
    check_container_environment
    echo ""
    
    show_environment_info
    echo ""
    
    wait_for_services
    echo ""
    
    install_dependencies
    echo ""
    
    cleanup_temp_files
    echo ""
    
    setup_database
    echo ""
    
    precompile_assets
    echo ""
    
    start_development_servers
}

# 处理命令行参数
case "${1:-start}" in
    "start")
        main
        ;;
    "check")
        ./scripts/check-devcontainer.sh
        ;;
    "rails")
        log_info "启动Rails服务器..."
        bundle exec rails server
        ;;
    "vite")
        log_info "启动Vite开发服务器..."
        bin/vite dev
        ;;
    "sidekiq")
        log_info "启动Sidekiq后台任务..."
        bundle exec sidekiq
        ;;
    "console")
        log_info "启动Rails控制台..."
        bundle exec rails console
        ;;
    "test")
        log_info "运行测试..."
        bundle exec rspec
        ;;
    "help")
        echo "用法: $0 [命令]"
        echo ""
        echo "可用命令:"
        echo "  start     初始化并准备开发环境 (默认)"
        echo "  check     检查环境状态"
        echo "  rails     启动Rails服务器"
        echo "  vite      启动Vite开发服务器"
        echo "  sidekiq   启动Sidekiq后台任务"
        echo "  console   启动Rails控制台"
        echo "  test      运行测试"
        echo "  help      显示此帮助信息"
        ;;
    *)
        log_error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
