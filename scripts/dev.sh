#!/bin/bash

# Chatwoot 开发环境管理脚本
# 使用方法: ./scripts/dev.sh [命令]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 配置文件
COMPOSE_FILE="docker-compose.dev.yml"
ENV_FILE=".env.development"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 检查环境文件
check_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "环境文件 $ENV_FILE 不存在，正在创建..."
        cp .env.development .env
        log_success "已创建环境文件 .env"
    fi
}

# 构建镜像
build() {
    log_info "构建 Docker 镜像..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    log_success "镜像构建完成"
}

# 启动服务
start() {
    log_info "启动 Chatwoot 开发环境..."
    check_dependencies
    check_env_file
    
    # 创建网络（如果不存在）
    docker network create chatwoot-network 2>/dev/null || true
    
    # 启动服务
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    status
    
    log_success "Chatwoot 开发环境启动完成！"
    log_info "访问地址："
    echo "  - 主应用: http://localhost:3000"
    echo "  - Vite 开发服务器: http://localhost:3036"
    echo "  - MailHog (邮件测试): http://localhost:8025"
    echo "  - Adminer (数据库管理): http://localhost:8080"
    echo "  - Redis Commander: http://localhost:8081"
}

# 停止服务
stop() {
    log_info "停止 Chatwoot 开发环境..."
    docker-compose -f "$COMPOSE_FILE" down
    log_success "服务已停止"
}

# 重启服务
restart() {
    log_info "重启 Chatwoot 开发环境..."
    stop
    start
}

# 查看服务状态
status() {
    log_info "服务状态："
    docker-compose -f "$COMPOSE_FILE" ps
}

# 查看日志
logs() {
    local service=${1:-}
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs -f "$service"
    else
        log_info "查看所有服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs -f
    fi
}

# 进入容器
shell() {
    local service=${1:-rails}
    log_info "进入 $service 容器..."
    docker-compose -f "$COMPOSE_FILE" exec "$service" /bin/sh
}

# 运行 Rails 命令
rails() {
    log_info "执行 Rails 命令: $*"
    docker-compose -f "$COMPOSE_FILE" exec rails bundle exec rails "$@"
}

# 数据库操作
db_setup() {
    log_info "设置数据库..."
    docker-compose -f "$COMPOSE_FILE" exec rails bundle exec rails db:create db:migrate db:seed
    log_success "数据库设置完成"
}

db_reset() {
    log_info "重置数据库..."
    docker-compose -f "$COMPOSE_FILE" exec rails bundle exec rails db:drop db:create db:migrate db:seed
    log_success "数据库重置完成"
}

db_migrate() {
    log_info "运行数据库迁移..."
    docker-compose -f "$COMPOSE_FILE" exec rails bundle exec rails db:migrate
    log_success "数据库迁移完成"
}

# 运行测试
test() {
    log_info "运行测试..."
    docker-compose -f "$COMPOSE_FILE" exec rails bundle exec rspec
}

# 清理
clean() {
    log_info "清理 Docker 资源..."
    docker-compose -f "$COMPOSE_FILE" down -v --remove-orphans
    docker system prune -f
    log_success "清理完成"
}

# 更新依赖
update() {
    log_info "更新依赖..."
    docker-compose -f "$COMPOSE_FILE" exec rails bundle install
    docker-compose -f "$COMPOSE_FILE" exec rails pnpm install
    log_success "依赖更新完成"
}

# 显示帮助信息
help() {
    echo "Chatwoot 开发环境管理脚本"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "可用命令："
    echo "  start          启动开发环境"
    echo "  stop           停止开发环境"
    echo "  restart        重启开发环境"
    echo "  status         查看服务状态"
    echo "  build          构建 Docker 镜像"
    echo "  logs [服务名]   查看日志"
    echo "  shell [服务名]  进入容器 (默认: rails)"
    echo "  rails [命令]    运行 Rails 命令"
    echo "  db:setup       设置数据库"
    echo "  db:reset       重置数据库"
    echo "  db:migrate     运行数据库迁移"
    echo "  test           运行测试"
    echo "  update         更新依赖"
    echo "  clean          清理 Docker 资源"
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0 start                    # 启动开发环境"
    echo "  $0 logs rails               # 查看 Rails 日志"
    echo "  $0 shell postgres           # 进入 PostgreSQL 容器"
    echo "  $0 rails console            # 运行 Rails 控制台"
    echo "  $0 rails generate model User # 生成 User 模型"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        status)
            status
            ;;
        build)
            build
            ;;
        logs)
            logs "${2:-}"
            ;;
        shell)
            shell "${2:-rails}"
            ;;
        rails)
            shift
            rails "$@"
            ;;
        db:setup)
            db_setup
            ;;
        db:reset)
            db_reset
            ;;
        db:migrate)
            db_migrate
            ;;
        test)
            test
            ;;
        update)
            update
            ;;
        clean)
            clean
            ;;
        help|--help|-h)
            help
            ;;
        *)
            log_error "未知命令: $1"
            help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
